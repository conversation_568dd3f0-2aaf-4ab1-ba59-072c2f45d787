# DeepSearch Configuration File

# Database Settings
database:
  url: "sqlite:///data/deepsearch.db"
  vector_db_path: "data/vector_db"
  backup_enabled: true
  backup_interval: 3600  # seconds

# Search Settings
search:
  max_results: 20
  default_language: "ar"
  timeout: 30
  engines:
    - brave
    - duckduckgo
  
# Scraping Settings
scraping:
  user_agent: "DeepSearch-Agent/1.0"
  request_delay: 1
  concurrent_requests: 5
  download_timeout: 30
  retry_attempts: 3
  respect_robots_txt: true
  
# AI Processing Settings
ai:
  provider: "mistral"  # ollama, openai, mistral, gemini
  model: "mistral-large-latest"
  fallback_provider: "ollama"  # Fallback if primary fails
  fallback_model: "mistral:latest"
  max_content_length: 5000  # Reduced to save tokens
  summary_max_length: 300   # Reduced to save tokens
  embedding_model: ""  # Disabled to avoid PyTorch issues
  temperature: 0.3  # Lower for more focused responses
  max_tokens: 1000  # Reduced to save tokens
  auto_switch: true  # Auto switch to fallback on failure
  # Rate limiting settings
  rate_limit_enabled: true
  requests_per_minute: 10  # Limit API calls
  batch_processing: true   # Process multiple items together
  skip_ai_on_short_content: true  # Skip AI for short articles

# API Settings
api:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  debug: true
  cors_enabled: true
  rate_limit: 100  # requests per minute

# Logging Settings
logging:
  level: "INFO"
  file: "logs/deepsearch.log"
  max_size: "10MB"
  backup_count: 5
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"

# Cache Settings
cache:
  enabled: true
  ttl: 3600  # seconds
  max_size: 1000  # number of entries

# News Engine Settings
news:
  max_age_hours: 24  # Only show news from last 24 hours
  max_history_days: 7  # Keep delivery history for 7 days
  max_full_content_length: 50000  # Maximum full content length
  preferred_sources:
    - "bbc.com"
    - "cnn.com"
    - "aljazeera.net"
    - "alarabiya.net"
    - "skynews.com"
    - "reuters.com"
    - "ap.org"
  enable_date_extraction: true
  enable_entity_extraction: true
  news_score_threshold: 1.0
  # Professional article settings
  professional_mode: true
  single_article_default: true
  max_articles_multi: 3
  extract_full_content_default: true

# Image Management Settings
images:
  # API keys for image providers (leave empty if not using)
  unsplash_api_key: ""  # Get from https://unsplash.com/developers
  pixabay_api_key: ""   # Get from https://pixabay.com/api/docs/
  pexels_api_key: ""    # Get from https://www.pexels.com/api/

  # Default settings
  default_provider: "unsplash"
  fallback_providers: ["pixabay", "pexels"]
  auto_fallback: true

  # Custom image generation settings
  custom_generation:
    enabled: true
    default_size: [1200, 630]  # Standard social media size
    output_format: "JPEG"
    quality: 90

  # Search settings
  default_per_page: 10
  max_per_page: 30
  default_orientation: "all"  # all, landscape, portrait, squarish
