"""
Custom image generator for creating article images
"""

import os
import io
import random
import time
from typing import Dict, List, Optional, Any, Tuple
from PIL import Image, ImageDraw, ImageFont
from pathlib import Path
import textwrap
from loguru import logger
import json
import base64


class CustomImageGenerator:
    """Generates custom images with text overlays"""
    
    def __init__(self, config_path: str = "config/image_generator.json"):
        self.config_path = Path(config_path)
        self.backgrounds_dir = Path("data/backgrounds")
        self.fonts_dir = Path("data/fonts")
        self.output_dir = Path("data/generated_images")
        
        # Create directories if they don't exist
        self.backgrounds_dir.mkdir(parents=True, exist_ok=True)
        self.fonts_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.config = self._load_config()
        self._ensure_default_assets()
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration"""
        default_config = {
            "default_size": [1200, 630],  # Standard social media size
            "text_styles": {
                "arabic": {
                    "font_family": "NotoSansArabic",
                    "font_size": 48,
                    "color": "#FFFFFF",
                    "stroke_width": 2,
                    "stroke_color": "#000000",
                    "shadow": True,
                    "shadow_offset": [2, 2],
                    "shadow_color": "#000000"
                },
                "english": {
                    "font_family": "Arial",
                    "font_size": 48,
                    "color": "#FFFFFF",
                    "stroke_width": 2,
                    "stroke_color": "#000000",
                    "shadow": True,
                    "shadow_offset": [2, 2],
                    "shadow_color": "#000000"
                }
            },
            "backgrounds": [],
            "overlay_opacity": 0.6,
            "text_margin": 50,
            "max_text_width": 0.8
        }
        
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    # Merge with defaults
                    for key, value in default_config.items():
                        if key not in config:
                            config[key] = value
                    return config
            except Exception as e:
                logger.error(f"Failed to load image generator config: {str(e)}")
        
        return default_config
    
    def _save_config(self):
        """Save configuration"""
        try:
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            logger.error(f"Failed to save image generator config: {str(e)}")
    
    def _ensure_default_assets(self):
        """Ensure default assets exist"""
        # Create a default background if none exist
        if not list(self.backgrounds_dir.glob("*")):
            self._create_default_background()
        
        # Update backgrounds list in config
        backgrounds = []
        for bg_file in self.backgrounds_dir.glob("*"):
            if bg_file.suffix.lower() in ['.jpg', '.jpeg', '.png', '.webp']:
                backgrounds.append(str(bg_file.name))
        
        self.config["backgrounds"] = backgrounds
        self._save_config()
    
    def _create_default_background(self):
        """Create a default gradient background"""
        try:
            width, height = self.config["default_size"]
            
            # Create gradient background
            image = Image.new('RGB', (width, height))
            draw = ImageDraw.Draw(image)
            
            # Create a gradient from blue to purple
            for y in range(height):
                r = int(75 + (150 * y / height))  # 75 to 225
                g = int(0 + (100 * y / height))   # 0 to 100
                b = int(130 + (125 * y / height)) # 130 to 255
                color = (r, g, b)
                draw.line([(0, y), (width, y)], fill=color)
            
            # Save default background
            bg_path = self.backgrounds_dir / "default_gradient.jpg"
            image.save(bg_path, "JPEG", quality=90)
            logger.info(f"Created default background: {bg_path}")
            
        except Exception as e:
            logger.error(f"Failed to create default background: {str(e)}")
    
    def add_background(self, image_path: str, name: Optional[str] = None) -> bool:
        """Add a new background image"""
        try:
            source_path = Path(image_path)
            if not source_path.exists():
                logger.error(f"Background image not found: {image_path}")
                return False
            
            # Generate name if not provided
            if not name:
                name = source_path.stem
            
            # Copy to backgrounds directory
            dest_path = self.backgrounds_dir / f"{name}{source_path.suffix}"
            
            # Resize and optimize the background
            with Image.open(source_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize to standard size
                target_size = tuple(self.config["default_size"])
                img = img.resize(target_size, Image.Resampling.LANCZOS)
                
                # Save optimized version
                img.save(dest_path, "JPEG", quality=85)
            
            # Update config
            if dest_path.name not in self.config["backgrounds"]:
                self.config["backgrounds"].append(dest_path.name)
                self._save_config()
            
            logger.info(f"Added background: {dest_path.name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to add background: {str(e)}")
            return False
    
    def get_available_backgrounds(self) -> List[str]:
        """Get list of available backgrounds"""
        return self.config.get("backgrounds", [])
    
    def _get_font_path(self, font_family: str, size: int) -> Optional[str]:
        """Get font path for the specified font family"""
        # Try to find font file
        font_extensions = ['.ttf', '.otf']
        
        for ext in font_extensions:
            font_path = self.fonts_dir / f"{font_family}{ext}"
            if font_path.exists():
                return str(font_path)
        
        # Try system fonts (this is platform-specific)
        try:
            # For Windows
            if os.name == 'nt':
                system_fonts = [
                    f"C:/Windows/Fonts/{font_family}.ttf",
                    f"C:/Windows/Fonts/{font_family}.otf"
                ]
                for font_path in system_fonts:
                    if os.path.exists(font_path):
                        return font_path
            
            # For Linux/Mac, try common locations
            else:
                system_fonts = [
                    f"/usr/share/fonts/truetype/{font_family.lower()}/{font_family}.ttf",
                    f"/System/Library/Fonts/{font_family}.ttf",
                    f"/Library/Fonts/{font_family}.ttf"
                ]
                for font_path in system_fonts:
                    if os.path.exists(font_path):
                        return font_path
        except Exception:
            pass
        
        return None
    
    def _detect_language(self, text: str) -> str:
        """Detect if text is Arabic or English"""
        arabic_chars = sum(1 for char in text if '\u0600' <= char <= '\u06FF')
        total_chars = len([char for char in text if char.isalpha()])
        
        if total_chars == 0:
            return "english"
        
        arabic_ratio = arabic_chars / total_chars
        return "arabic" if arabic_ratio > 0.5 else "english"
    
    def _wrap_text(self, text: str, font: ImageFont.ImageFont, max_width: int) -> List[str]:
        """Wrap text to fit within specified width"""
        words = text.split()
        lines = []
        current_line = []
        
        for word in words:
            test_line = ' '.join(current_line + [word])
            bbox = font.getbbox(test_line)
            text_width = bbox[2] - bbox[0]
            
            if text_width <= max_width:
                current_line.append(word)
            else:
                if current_line:
                    lines.append(' '.join(current_line))
                    current_line = [word]
                else:
                    # Word is too long, force break
                    lines.append(word)
        
        if current_line:
            lines.append(' '.join(current_line))
        
        return lines
    
    def generate_image(
        self,
        title: str,
        background: Optional[str] = None,
        style: str = "auto",
        size: Optional[Tuple[int, int]] = None,
        output_format: str = "JPEG"
    ) -> Dict[str, Any]:
        """Generate a custom image with title overlay"""
        try:
            # Determine size
            if not size:
                size = tuple(self.config["default_size"])
            
            # Select background
            if not background:
                available_backgrounds = self.get_available_backgrounds()
                if not available_backgrounds:
                    return {
                        "success": False,
                        "error": "No backgrounds available"
                    }
                background = random.choice(available_backgrounds)
            
            # Load background image
            bg_path = self.backgrounds_dir / background
            if not bg_path.exists():
                return {
                    "success": False,
                    "error": f"Background not found: {background}"
                }
            
            with Image.open(bg_path) as bg_img:
                # Resize background to target size
                bg_img = bg_img.resize(size, Image.Resampling.LANCZOS)
                
                # Convert to RGB if necessary
                if bg_img.mode != 'RGB':
                    bg_img = bg_img.convert('RGB')
                
                # Create overlay for better text readability
                overlay = Image.new('RGBA', size, (0, 0, 0, int(255 * self.config["overlay_opacity"])))
                bg_img = Image.alpha_composite(bg_img.convert('RGBA'), overlay).convert('RGB')
                
                # Detect language or use specified style
                if style == "auto":
                    language = self._detect_language(title)
                else:
                    language = style
                
                # Get text style
                text_style = self.config["text_styles"].get(language, self.config["text_styles"]["english"])
                
                # Load font
                font_path = self._get_font_path(text_style["font_family"], text_style["font_size"])
                try:
                    if font_path:
                        font = ImageFont.truetype(font_path, text_style["font_size"])
                    else:
                        font = ImageFont.load_default()
                except Exception:
                    font = ImageFont.load_default()
                
                # Calculate text area
                margin = self.config["text_margin"]
                max_text_width = int(size[0] * self.config["max_text_width"])
                
                # Wrap text
                lines = self._wrap_text(title, font, max_text_width)
                
                # Calculate total text height
                line_height = font.getbbox("Ay")[3] - font.getbbox("Ay")[1] + 10
                total_text_height = len(lines) * line_height
                
                # Position text in center
                start_y = (size[1] - total_text_height) // 2
                
                # Draw text
                draw = ImageDraw.Draw(bg_img)
                
                for i, line in enumerate(lines):
                    bbox = font.getbbox(line)
                    text_width = bbox[2] - bbox[0]
                    x = (size[0] - text_width) // 2
                    y = start_y + i * line_height
                    
                    # Draw shadow if enabled
                    if text_style.get("shadow", False):
                        shadow_offset = text_style.get("shadow_offset", [2, 2])
                        shadow_color = text_style.get("shadow_color", "#000000")
                        draw.text(
                            (x + shadow_offset[0], y + shadow_offset[1]),
                            line,
                            font=font,
                            fill=shadow_color
                        )
                    
                    # Draw main text with stroke
                    if text_style.get("stroke_width", 0) > 0:
                        stroke_width = text_style["stroke_width"]
                        stroke_color = text_style.get("stroke_color", "#000000")
                        draw.text(
                            (x, y),
                            line,
                            font=font,
                            fill=text_style["color"],
                            stroke_width=stroke_width,
                            stroke_fill=stroke_color
                        )
                    else:
                        draw.text(
                            (x, y),
                            line,
                            font=font,
                            fill=text_style["color"]
                        )
                
                # Save image
                timestamp = int(time.time())
                filename = f"article_image_{timestamp}.{output_format.lower()}"
                output_path = self.output_dir / filename
                
                bg_img.save(output_path, output_format, quality=90)
                
                # Convert to base64 for API response
                buffer = io.BytesIO()
                bg_img.save(buffer, output_format, quality=90)
                image_base64 = base64.b64encode(buffer.getvalue()).decode()
                
                return {
                    "success": True,
                    "image_path": str(output_path),
                    "filename": filename,
                    "image_base64": image_base64,
                    "size": size,
                    "background_used": background,
                    "language_detected": language,
                    "title": title
                }
                
        except Exception as e:
            logger.error(f"Failed to generate image: {str(e)}")
            return {
                "success": False,
                "error": f"Image generation failed: {str(e)}"
            }
    
    def update_text_style(self, language: str, style_updates: Dict[str, Any]) -> bool:
        """Update text style for a language"""
        try:
            if language not in self.config["text_styles"]:
                self.config["text_styles"][language] = {}
            
            self.config["text_styles"][language].update(style_updates)
            self._save_config()
            return True
        except Exception as e:
            logger.error(f"Failed to update text style: {str(e)}")
            return False
    
    def get_text_styles(self) -> Dict[str, Any]:
        """Get current text styles"""
        return self.config.get("text_styles", {})
    
    def get_stats(self) -> Dict[str, Any]:
        """Get generator statistics"""
        try:
            backgrounds_count = len(self.get_available_backgrounds())
            generated_images = len(list(self.output_dir.glob("*")))
            
            return {
                "backgrounds_available": backgrounds_count,
                "generated_images": generated_images,
                "output_directory": str(self.output_dir),
                "backgrounds_directory": str(self.backgrounds_dir),
                "fonts_directory": str(self.fonts_dir)
            }
        except Exception as e:
            logger.error(f"Failed to get generator stats: {str(e)}")
            return {}
