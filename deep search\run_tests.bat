@echo off
title DeepSearch Agent Pro - Test Suite

echo.
echo ========================================
echo   DeepSearch Agent Pro - Test Suite
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

echo Python found. Starting comprehensive tests...
echo.
echo This will test all system components:
echo - Basic components and configuration
echo - Advanced keyword extraction
echo - Deep search functionality  
echo - Article generation with Gemini AI
echo - SEO optimization features
echo - Multi-language and dialect support
echo - Automatic news discovery
echo - Publishing capabilities
echo - System integration
echo.
echo A web interface will open with detailed results.
echo.

REM Run the test suite
python test_runner.py

echo.
echo Tests completed. Check the web interface for results.
echo Press any key to exit...
pause >nul
