"""
Images API Routes
Provides endpoints for image search and generation
"""

from fastapi import APIRouter, HTTPException, Depends, UploadFile, File
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from loguru import logger
import tempfile
import os

from ..images.image_manager import ImageManager
from ..utils.config_loader import load_config


router = APIRouter(prefix="/images", tags=["images"])

# Global image manager instance
image_manager: Optional[ImageManager] = None


def get_image_manager() -> ImageManager:
    """Get image manager instance"""
    global image_manager
    if image_manager is None:
        config = load_config()
        image_manager = ImageManager(config)
    return image_manager


class ImageSearchRequest(BaseModel):
    """Image search request model"""
    query: str
    provider: Optional[str] = None
    per_page: int = 10
    orientation: str = "all"  # all, landscape, portrait, squarish
    category: Optional[str] = None
    use_fallback: bool = True


class MultiProviderSearchRequest(BaseModel):
    """Multi-provider search request model"""
    query: str
    providers: Optional[List[str]] = None
    per_page: int = 5


class CustomImageRequest(BaseModel):
    """Custom image generation request model"""
    title: str
    background: Optional[str] = None
    style: str = "auto"  # auto, arabic, english
    size: Optional[List[int]] = None  # [width, height]


class ConfigureProviderRequest(BaseModel):
    """Provider configuration request model"""
    provider: str
    api_key: str


class UpdateTextStyleRequest(BaseModel):
    """Text style update request model"""
    language: str
    style_updates: Dict[str, Any]


class ArticleImageRequest(BaseModel):
    """Article image request model"""
    title: str
    category: Optional[str] = None
    prefer_custom: bool = False
    keywords: Optional[List[str]] = None


@router.get("/status")
async def get_image_manager_status(
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Get status of image manager and all providers
    
    Returns information about configured providers and custom generator status.
    """
    try:
        status = manager.get_status()
        return {
            "success": True,
            "status": status
        }
    except Exception as e:
        logger.error(f"Failed to get image manager status: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/configure")
async def configure_provider(
    request: ConfigureProviderRequest,
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Configure an image provider with API key
    
    Configures the specified provider (unsplash, pixabay, pexels) with the provided API key.
    """
    try:
        success = manager.configure_provider(request.provider, request.api_key)
        
        if success:
            return {
                "success": True,
                "message": f"Successfully configured {request.provider} provider",
                "provider": request.provider
            }
        else:
            raise HTTPException(
                status_code=400,
                detail=f"Failed to configure {request.provider} provider"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to configure provider: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search")
async def search_images(
    request: ImageSearchRequest,
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Search for images using specified or default provider
    
    Searches for images matching the query using the specified provider or falls back to others.
    """
    try:
        result = await manager.search_images(
            query=request.query,
            provider=request.provider,
            per_page=request.per_page,
            orientation=request.orientation,
            category=request.category,
            use_fallback=request.use_fallback
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Image search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/search/multi")
async def search_multiple_providers(
    request: MultiProviderSearchRequest,
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Search multiple providers simultaneously
    
    Searches for images across multiple providers and returns combined results.
    """
    try:
        result = await manager.search_multiple_providers(
            query=request.query,
            providers=request.providers,
            per_page=request.per_page
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Multi-provider search failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/generate")
async def generate_custom_image(
    request: CustomImageRequest,
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Generate a custom image with title overlay
    
    Creates a custom image with the provided title overlaid on a background.
    """
    try:
        size = tuple(request.size) if request.size else None
        
        result = manager.generate_custom_image(
            title=request.title,
            background=request.background,
            style=request.style,
            size=size
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Custom image generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/backgrounds/upload")
async def upload_background(
    file: UploadFile = File(...),
    name: Optional[str] = None,
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Upload a new background image
    
    Uploads and adds a new background image for custom image generation.
    """
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as tmp_file:
            content = await file.read()
            tmp_file.write(content)
            tmp_file_path = tmp_file.name
        
        try:
            # Add background
            success = manager.add_background(tmp_file_path, name or file.filename)
            
            if success:
                return {
                    "success": True,
                    "message": "Background uploaded successfully",
                    "filename": name or file.filename
                }
            else:
                raise HTTPException(status_code=500, detail="Failed to add background")
                
        finally:
            # Clean up temporary file
            os.unlink(tmp_file_path)
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Background upload failed: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/backgrounds")
async def get_available_backgrounds(
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Get list of available background images
    
    Returns a list of all available background images for custom generation.
    """
    try:
        backgrounds = manager.get_available_backgrounds()
        return {
            "success": True,
            "backgrounds": backgrounds,
            "total_count": len(backgrounds)
        }
    except Exception as e:
        logger.error(f"Failed to get backgrounds: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/text-styles")
async def get_text_styles(
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Get current text styles for custom image generation
    
    Returns the current text styling configuration for different languages.
    """
    try:
        styles = manager.get_text_styles()
        return {
            "success": True,
            "text_styles": styles
        }
    except Exception as e:
        logger.error(f"Failed to get text styles: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/text-styles")
async def update_text_style(
    request: UpdateTextStyleRequest,
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Update text style for a specific language
    
    Updates the text styling configuration for the specified language.
    """
    try:
        success = manager.update_text_style(request.language, request.style_updates)
        
        if success:
            return {
                "success": True,
                "message": f"Text style updated for {request.language}",
                "language": request.language
            }
        else:
            raise HTTPException(status_code=500, detail="Failed to update text style")
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update text style: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/article")
async def get_image_for_article(
    request: ArticleImageRequest,
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Get the best image for an article
    
    Automatically selects the best image for an article, either from providers or custom generation.
    """
    try:
        result = await manager.get_image_for_article(
            title=request.title,
            category=request.category,
            prefer_custom=request.prefer_custom,
            keywords=request.keywords
        )
        
        return result
        
    except Exception as e:
        logger.error(f"Failed to get article image: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/providers")
async def get_configured_providers(
    manager: ImageManager = Depends(get_image_manager)
):
    """
    Get list of configured image providers
    
    Returns a list of all configured and available image providers.
    """
    try:
        configured = manager.get_configured_providers()
        all_providers = list(manager.providers.keys())
        
        return {
            "success": True,
            "configured_providers": configured,
            "all_providers": all_providers,
            "default_provider": manager.default_provider
        }
    except Exception as e:
        logger.error(f"Failed to get providers: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
