"""
News Categories Management System
Manages different news categories and their configurations
"""

from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum
import json
from pathlib import Path
from loguru import logger


class CategoryType(Enum):
    """News category types"""
    GAMING = "gaming"
    CRYPTOCURRENCY = "cryptocurrency"
    FOOTBALL = "football"
    POLITICS = "politics"
    TECHNOLOGY = "technology"
    SPORTS = "sports"
    ENTERTAINMENT = "entertainment"
    BUSINESS = "business"
    HEALTH = "health"
    SCIENCE = "science"
    WORLD_NEWS = "world_news"
    LOCAL_NEWS = "local_news"
    CUSTOM = "custom"


@dataclass
class NewsCategory:
    """News category configuration"""
    id: str
    name_ar: str
    name_en: str
    description_ar: str
    description_en: str
    keywords_ar: List[str]
    keywords_en: List[str]
    search_terms_ar: List[str]
    search_terms_en: List[str]
    category_type: CategoryType
    is_active: bool = True
    priority: int = 1
    color: str = "#007bff"
    icon: str = "fas fa-newspaper"
    custom_prompts: Optional[Dict[str, str]] = None
    seo_keywords: Optional[List[str]] = None


class NewsCategoriesManager:
    """Manages news categories and their configurations"""
    
    def __init__(self, config_path: str = "config/news_categories.json"):
        self.config_path = Path(config_path)
        self.categories: Dict[str, NewsCategory] = {}
        self._load_default_categories()
        self._load_custom_categories()
    
    def _load_default_categories(self):
        """Load default news categories"""
        default_categories = [
            NewsCategory(
                id="gaming",
                name_ar="الألعاب",
                name_en="Gaming",
                description_ar="أخبار الألعاب الإلكترونية والتطورات في عالم الألعاب",
                description_en="Video game news and gaming industry developments",
                keywords_ar=["ألعاب", "لعبة", "جيمنج", "بلايستيشن", "إكسبوكس", "نينتندو", "PC"],
                keywords_en=["gaming", "games", "PlayStation", "Xbox", "Nintendo", "PC", "esports"],
                search_terms_ar=[
                    "آخر أخبار الألعاب",
                    "أخبار الألعاب الإلكترونية",
                    "تطورات عالم الألعاب",
                    "أخبار PlayStation",
                    "أخبار Xbox",
                    "ألعاب جديدة"
                ],
                search_terms_en=[
                    "latest gaming news",
                    "video game news",
                    "gaming industry updates",
                    "new game releases",
                    "esports news"
                ],
                category_type=CategoryType.GAMING,
                color="#9c27b0",
                icon="fas fa-gamepad",
                custom_prompts={
                    "article_style": "اكتب مقالاً احترافياً عن الألعاب بأسلوب شيق ومثير للاهتمام",
                    "tone": "متحمس ومتخصص في عالم الألعاب"
                }
            ),
            NewsCategory(
                id="cryptocurrency",
                name_ar="العملات الرقمية",
                name_en="Cryptocurrency",
                description_ar="أخبار العملات الرقمية والبلوك تشين والتطورات المالية الرقمية",
                description_en="Cryptocurrency, blockchain, and digital finance news",
                keywords_ar=["بيتكوين", "عملات رقمية", "بلوك تشين", "إيثيريوم", "تداول", "استثمار"],
                keywords_en=["bitcoin", "cryptocurrency", "blockchain", "ethereum", "trading", "DeFi"],
                search_terms_ar=[
                    "أخبار البيتكوين",
                    "أخبار العملات الرقمية",
                    "تطورات البلوك تشين",
                    "أسعار العملات الرقمية",
                    "استثمار العملات الرقمية"
                ],
                search_terms_en=[
                    "bitcoin news",
                    "cryptocurrency news",
                    "blockchain updates",
                    "crypto market news",
                    "DeFi news"
                ],
                category_type=CategoryType.CRYPTOCURRENCY,
                color="#ff9800",
                icon="fab fa-bitcoin",
                custom_prompts={
                    "article_style": "اكتب مقالاً تحليلياً عن العملات الرقمية مع التركيز على الجوانب التقنية والاستثمارية",
                    "tone": "تحليلي ومهني مع تجنب النصائح الاستثمارية المباشرة"
                }
            ),
            NewsCategory(
                id="football",
                name_ar="كرة القدم",
                name_en="Football",
                description_ar="أخبار كرة القدم المحلية والعالمية والدوريات الكبرى",
                description_en="Local and international football news and major leagues",
                keywords_ar=["كرة القدم", "دوري", "مباراة", "لاعب", "نادي", "بطولة", "كأس العالم"],
                keywords_en=["football", "soccer", "league", "match", "player", "club", "world cup"],
                search_terms_ar=[
                    "أخبار كرة القدم",
                    "أخبار الدوري السعودي",
                    "أخبار دوري أبطال أوروبا",
                    "انتقالات اللاعبين",
                    "نتائج المباريات"
                ],
                search_terms_en=[
                    "football news",
                    "soccer news",
                    "premier league news",
                    "champions league news",
                    "transfer news"
                ],
                category_type=CategoryType.FOOTBALL,
                color="#4caf50",
                icon="fas fa-futbol",
                custom_prompts={
                    "article_style": "اكتب مقالاً رياضياً مثيراً عن كرة القدم مع التركيز على التفاصيل والإحصائيات",
                    "tone": "متحمس ورياضي مع استخدام المصطلحات الكروية"
                }
            ),
            NewsCategory(
                id="politics",
                name_ar="السياسة",
                name_en="Politics",
                description_ar="الأخبار السياسية المحلية والعالمية والتطورات الحكومية",
                description_en="Local and international political news and government developments",
                keywords_ar=["سياسة", "حكومة", "انتخابات", "رئيس", "وزير", "برلمان", "دبلوماسية"],
                keywords_en=["politics", "government", "elections", "president", "minister", "parliament"],
                search_terms_ar=[
                    "الأخبار السياسية",
                    "أخبار الحكومة",
                    "التطورات السياسية",
                    "الأخبار الدبلوماسية",
                    "أخبار الانتخابات"
                ],
                search_terms_en=[
                    "political news",
                    "government news",
                    "political developments",
                    "diplomatic news",
                    "election news"
                ],
                category_type=CategoryType.POLITICS,
                color="#f44336",
                icon="fas fa-landmark",
                custom_prompts={
                    "article_style": "اكتب مقالاً سياسياً متوازناً وموضوعياً مع تجنب التحيز",
                    "tone": "رسمي ومهني مع التركيز على الحقائق"
                }
            ),
            NewsCategory(
                id="technology",
                name_ar="التكنولوجيا",
                name_en="Technology",
                description_ar="أخبار التكنولوجيا والذكاء الاصطناعي والابتكارات التقنية",
                description_en="Technology, AI, and innovation news",
                keywords_ar=["تكنولوجيا", "ذكاء اصطناعي", "تطبيق", "هاتف", "كمبيوتر", "إنترنت"],
                keywords_en=["technology", "AI", "artificial intelligence", "app", "smartphone", "computer"],
                search_terms_ar=[
                    "أخبار التكنولوجيا",
                    "أخبار الذكاء الاصطناعي",
                    "تطورات تقنية",
                    "أخبار الهواتف الذكية",
                    "ابتكارات تقنية"
                ],
                search_terms_en=[
                    "technology news",
                    "AI news",
                    "tech updates",
                    "smartphone news",
                    "innovation news"
                ],
                category_type=CategoryType.TECHNOLOGY,
                color="#2196f3",
                icon="fas fa-microchip",
                custom_prompts={
                    "article_style": "اكتب مقالاً تقنياً مفصلاً مع شرح المفاهيم التقنية بطريقة مبسطة",
                    "tone": "تقني ومتخصص مع إمكانية الفهم للجمهور العام"
                }
            )
        ]
        
        for category in default_categories:
            self.categories[category.id] = category
    
    def _load_custom_categories(self):
        """Load custom categories from config file"""
        if self.config_path.exists():
            try:
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                for category_data in data.get('custom_categories', []):
                    category = NewsCategory(**category_data)
                    self.categories[category.id] = category
                    
                logger.info(f"Loaded {len(data.get('custom_categories', []))} custom categories")
                
            except Exception as e:
                logger.error(f"Failed to load custom categories: {str(e)}")
    
    def save_custom_categories(self):
        """Save custom categories to config file"""
        try:
            # Create config directory if it doesn't exist
            self.config_path.parent.mkdir(parents=True, exist_ok=True)
            
            custom_categories = [
                asdict(category) for category in self.categories.values()
                if category.category_type == CategoryType.CUSTOM
            ]
            
            data = {
                'custom_categories': custom_categories,
                'last_updated': str(Path(__file__).stat().st_mtime)
            }
            
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
            logger.info(f"Saved {len(custom_categories)} custom categories")
            
        except Exception as e:
            logger.error(f"Failed to save custom categories: {str(e)}")
    
    def get_category(self, category_id: str) -> Optional[NewsCategory]:
        """Get category by ID"""
        return self.categories.get(category_id)
    
    def get_all_categories(self, active_only: bool = True) -> List[NewsCategory]:
        """Get all categories"""
        categories = list(self.categories.values())
        if active_only:
            categories = [cat for cat in categories if cat.is_active]
        return sorted(categories, key=lambda x: (x.priority, x.name_ar))
    
    def get_categories_by_type(self, category_type: CategoryType) -> List[NewsCategory]:
        """Get categories by type"""
        return [cat for cat in self.categories.values() if cat.category_type == category_type]

    def add_custom_category(self, category: NewsCategory) -> bool:
        """Add a custom category"""
        try:
            category.category_type = CategoryType.CUSTOM
            self.categories[category.id] = category
            self.save_custom_categories()
            logger.info(f"Added custom category: {category.name_ar}")
            return True
        except Exception as e:
            logger.error(f"Failed to add custom category: {str(e)}")
            return False

    def update_category(self, category_id: str, updates: Dict[str, Any]) -> bool:
        """Update category"""
        try:
            if category_id not in self.categories:
                return False

            category = self.categories[category_id]
            for key, value in updates.items():
                if hasattr(category, key):
                    setattr(category, key, value)

            if category.category_type == CategoryType.CUSTOM:
                self.save_custom_categories()

            logger.info(f"Updated category: {category.name_ar}")
            return True
        except Exception as e:
            logger.error(f"Failed to update category: {str(e)}")
            return False

    def delete_custom_category(self, category_id: str) -> bool:
        """Delete a custom category"""
        try:
            if category_id not in self.categories:
                return False

            category = self.categories[category_id]
            if category.category_type != CategoryType.CUSTOM:
                logger.warning(f"Cannot delete default category: {category_id}")
                return False

            del self.categories[category_id]
            self.save_custom_categories()
            logger.info(f"Deleted custom category: {category.name_ar}")
            return True
        except Exception as e:
            logger.error(f"Failed to delete custom category: {str(e)}")
            return False

    def get_search_terms_for_category(self, category_id: str, language: str = "ar") -> List[str]:
        """Get search terms for a category"""
        category = self.get_category(category_id)
        if not category:
            return []

        if language == "ar":
            return category.search_terms_ar
        else:
            return category.search_terms_en

    def get_keywords_for_category(self, category_id: str, language: str = "ar") -> List[str]:
        """Get keywords for a category"""
        category = self.get_category(category_id)
        if not category:
            return []

        if language == "ar":
            return category.keywords_ar
        else:
            return category.keywords_en

    def get_category_prompt(self, category_id: str, prompt_type: str = "article_style") -> Optional[str]:
        """Get custom prompt for a category"""
        category = self.get_category(category_id)
        if not category or not category.custom_prompts:
            return None

        return category.custom_prompts.get(prompt_type)

    def search_categories(self, query: str, language: str = "ar") -> List[NewsCategory]:
        """Search categories by name or keywords"""
        query = query.lower()
        results = []

        for category in self.categories.values():
            if not category.is_active:
                continue

            # Search in names
            if language == "ar":
                if query in category.name_ar.lower() or query in category.description_ar.lower():
                    results.append(category)
                    continue
                # Search in keywords
                if any(query in keyword.lower() for keyword in category.keywords_ar):
                    results.append(category)
            else:
                if query in category.name_en.lower() or query in category.description_en.lower():
                    results.append(category)
                    continue
                # Search in keywords
                if any(query in keyword.lower() for keyword in category.keywords_en):
                    results.append(category)

        return results

    def get_category_stats(self) -> Dict[str, Any]:
        """Get statistics about categories"""
        total_categories = len(self.categories)
        active_categories = len([cat for cat in self.categories.values() if cat.is_active])
        custom_categories = len([cat for cat in self.categories.values() if cat.category_type == CategoryType.CUSTOM])

        type_counts = {}
        for category_type in CategoryType:
            count = len(self.get_categories_by_type(category_type))
            if count > 0:
                type_counts[category_type.value] = count

        return {
            "total_categories": total_categories,
            "active_categories": active_categories,
            "custom_categories": custom_categories,
            "default_categories": total_categories - custom_categories,
            "categories_by_type": type_counts
        }
