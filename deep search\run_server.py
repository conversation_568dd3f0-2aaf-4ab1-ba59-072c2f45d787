#!/usr/bin/env python3
"""
DeepSearch Enhanced News App Server
Starts the FastAPI server with all enhanced features
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_loader import load_config
from src.database.database import init_database


def main():
    """Start the enhanced news app server"""
    
    print("🚀 Starting DeepSearch Enhanced News App...")
    print("=" * 60)
    
    # Load configuration
    try:
        config = load_config()
        print("✅ Configuration loaded successfully")
    except Exception as e:
        print(f"❌ Failed to load configuration: {e}")
        sys.exit(1)
    
    # Initialize database
    try:
        init_database()
        print("✅ Database initialized successfully")
    except Exception as e:
        print(f"❌ Failed to initialize database: {e}")
        sys.exit(1)
    
    print("\n🌟 Enhanced Features Available:")
    print("   🏷️  Category Management - Organize news by topics")
    print("   🖼️  Image Search & Generation - Free licensed images")
    print("   📝  Professional Article Generation - High-quality content")
    print("   🎨  Custom Image Creation - Branded article images")
    print("   ⚙️  Advanced Settings - Full API configuration")
    print("   📱  Modern Web Interface - User-friendly design")
    
    print("\n🔗 Access Points:")
    print("   📱 Enhanced Web Interface: http://localhost:8000/enhanced_news_app.html")
    print("   📊 API Documentation: http://localhost:8000/docs")
    print("   🔧 Original Interface: http://localhost:8000/professional.html")
    print("   🔍 Interactive API: http://localhost:8000/redoc")
    
    print("\n📋 API Endpoints:")
    print("   /categories/ - News category management")
    print("   /images/ - Image search and generation")
    print("   /news/ - News search and analysis")
    print("   /articles/ - Article generation")
    print("   /publishing/ - Blogger publishing")
    
    print("\n🔧 Configuration Tips:")
    print("   • Add image API keys in Settings tab for better image search")
    print("   • Configure Blogger API for direct publishing")
    print("   • Upload custom backgrounds for branded images")
    print("   • Create custom categories for specialized content")
    
    print("\n" + "=" * 60)
    print("🎯 Ready to create professional Arabic content!")
    print("=" * 60)
    
    # Start the server
    import uvicorn
    uvicorn.run(
        "src.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        sys.exit(1)
