package com.modetaris.gamingnews

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.webkit.*
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity

class MainActivity : AppCompatActivity() {

    private lateinit var webView: WebView

    @SuppressLint("SetJavaScriptEnabled")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_web_main)

        try {
            setupWebView()
            loadWebInterface()
        } catch (e: Exception) {
            Toast.makeText(this, "خطأ في تحميل التطبيق: ${e.message}", Toast.LENGTH_LONG).show()
            finish()
        }
    }

    private fun setupWebView() {
        webView = findViewById(R.id.webView)

        webView.settings.apply {
            javaScriptEnabled = true
            domStorageEnabled = true
            allowFileAccess = true
            allowContentAccess = true
        }

        webView.webViewClient = object : WebViewClient() {
            override fun onPageFinished(view: WebView?, url: String?) {
                super.onPageFinished(view, url)
                // Page loaded successfully
            }
        }

        // JavaScript interface will be handled differently
    }

    private fun loadWebInterface() {
        val htmlContent = """
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🎮 تطبيق أخبار الألعاب</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header { text-align: center; margin-bottom: 30px; }
        .header h1 { font-size: 2.5em; color: #4a5568; margin-bottom: 10px; }
        .input-group { margin-bottom: 20px; }
        label { display: block; margin-bottom: 8px; font-weight: 600; color: #4a5568; }
        input, select {
            width: 100%;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 12px;
            font-size: 16px;
        }
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            margin-bottom: 10px;
        }
        .result-section {
            margin-top: 30px;
            padding: 20px;
            background: rgba(247, 250, 252, 0.8);
            border-radius: 12px;
            display: none;
        }
        .result-section.show { display: block; }
        .loading { text-align: center; padding: 40px; }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .article-preview {
            background: white;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
        }
        .article-title {
            font-size: 1.5em;
            font-weight: 700;
            color: #2d3748;
            margin-bottom: 15px;
        }
        .article-content {
            line-height: 1.8;
            color: #4a5568;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 تطبيق أخبار الألعاب</h1>
            <p>إنشاء مقالات احترافية بالذكاء الاصطناعي</p>
        </div>

        <div class="input-group">
            <label for="searchQuery">🔍 موضوع المقال</label>
            <input type="text" id="searchQuery" placeholder="مثال: أحدث ألعاب PlayStation 5">
        </div>


        <div class="input-group">
            <label for="providerSelect">🤖 مزود الذكاء الاصطناعي</label>
            <select id="providerSelect">
                <option value="offline">بدون مزود (وضع غير متصل - مجاني)</option>
                <option value="gemini">Google Gemini (محدود مجاناً - يتطلب API Key)</option>
                <option value="mistral">Mistral (تجريبي/محدود - يتطلب API Key)</option>
                <option value="openrouter">OpenRouter (نماذج متعددة - يتطلب API Key)</option>
                <option value="ollama">Ollama (محلي/خادم ذاتي - مجاني)</option>
            </select>
        </div>

        <div class="input-group">
            <label for="modelSelect">🧠 النموذج</label>
            <select id="modelSelect">
            </select>
        </div>

        <div class="input-group">
            <label for="articleType">📝 نوع المقال</label>
            <select id="articleType">
                <option value="formal">📋 رسمي</option>
                <option value="friendly">😊 ودي</option>
                <option value="news">📰 إخباري</option>
                <option value="analytical">🔍 تحليلي</option>
            </select>
        </div>

        <button class="btn" onclick="generateArticle()">✨ إنشاء المقال</button>

        <div id="loadingSection" class="loading" style="display: none;">
            <div class="spinner"></div>
            <p>جاري إنشاء مقال احترافي...</p>
        </div>

        <div id="resultSection" class="result-section">
            <div class="article-preview">
                <div class="article-title" id="articleTitle"></div>
                <div class="article-content" id="articleContent"></div>
            </div>
            <button class="btn" onclick="copyToClipboard()">📋 نسخ HTML</button>
            <button class="btn" onclick="shareText()">📤 مشاركة</button>
            <button class="btn" onclick="generateNew()">🔄 مقال جديد</button>
        </div>
    </div>

    <script>
        let currentArticle = null;

        function generateArticle() {
            const query = document.getElementById('searchQuery').value.trim();
            const type = document.getElementById('articleType').value;

            if (!query) {
                alert('يرجى إدخال موضوع المقال');
                return;
            }

            showLoading(true);

            setTimeout(() => {
                const article = createArticle(query, type);
                displayArticle(article);
                showLoading(false);
            }, 3000);
        }

        function createArticle(query, type) {
            const titles = {
                formal: query + " - دليل شامل ومفصل",
                friendly: "🎮 كل اللي محتاج تعرفه عن " + query,
                news: "عاجل: آخر أخبار " + query,
                analytical: "تحليل عميق: " + query + " والمستقبل"
            };

            const content = "مرحباً بكم في دليلنا الشامل حول " + query + ". في هذا المقال، سنستكشف كل ما تحتاجون لمعرفته حول هذا الموضوع المثير في عالم الألعاب.\\n\\n## 🔍 نظرة عامة\\n\\nيعتبر " + query + " من المواضيع المهمة والمثيرة في عالم الألعاب والتكنولوجيا. سنقدم لكم معلومات دقيقة ومحدثة حول هذا الموضوع.\\n\\n## 📊 التفاصيل الرئيسية\\n\\n• معلومات أساسية حول " + query + "\\n• أحدث التطورات والأخبار\\n• نصائح وإرشادات مفيدة للاعبين\\n• توقعات مستقبلية ومراجعات شاملة\\n\\n## 🎮 الخلاصة\\n\\nفي الختام، " + query + " موضوع يستحق المتابعة والاهتمام. نأمل أن يكون هذا المقال مفيداً لجميع محبي الألعاب.\\n\\n---\\n*تم إنشاء هذا المقال بواسطة نظام الذكاء الاصطناعي المتقدم*";

            return {
                title: titles[type] || titles.formal,
                content: content
            };
        }

        function displayArticle(article) {
            currentArticle = article;
            document.getElementById('articleTitle').textContent = article.title;
            document.getElementById('articleContent').innerHTML = article.content.replace(/\\n/g, '<br>');
            document.getElementById('resultSection').classList.add('show');
        }

        function showLoading(show) {
            document.getElementById('loadingSection').style.display = show ? 'block' : 'none';
            document.getElementById('resultSection').classList.toggle('show', !show);
        }

        function copyToClipboard() {
            if (!currentArticle) return;
            const html = "<!DOCTYPE html>\\n<html dir=\\"rtl\\" lang=\\"ar\\">\\n<head>\\n    <meta charset=\\"UTF-8\\">\\n    <title>" + currentArticle.title + "</title>\\n</head>\\n<body>\\n    <h1>" + currentArticle.title + "</h1>\\n    <div>" + currentArticle.content.replace(/\\n/g, '<br>') + "</div>\\n</body>\\n</html>";

            // Copy to clipboard using navigator API
            if (navigator.clipboard) {
                navigator.clipboard.writeText(html).then(() => {
                    alert('تم نسخ HTML إلى الحافظة');
                }).catch(() => {
                    alert('خطأ في النسخ');
                });
            } else {
                alert('النسخ غير مدعوم في هذا المتصفح');
            }
        }

        function shareText() {
            if (!currentArticle) return;
            const text = currentArticle.title + "\\n\\n" + currentArticle.content;

            // Use Web Share API if available
            if (navigator.share) {
                navigator.share({
                    title: currentArticle.title,
                    text: text
                }).catch(() => {
                    alert('خطأ في المشاركة');

        const providerModels = {
            offline: ["offline-basic"],
            gemini: ["gemini-2.0-flash-exp", "gemini-1.5-pro", "gemini-1.5-flash"],
            mistral: ["mistral-small-latest", "mistral-medium-latest", "mistral-large-latest"],
            openrouter: ["qwen2.5:7b-instruct", "llama-3.1:8b-instruct", "mixtral-8x7b-instruct"],
            ollama: ["mistral:latest", "llama3.1:8b", "qwen2.5:7b"]
        };

        function populateModels() {
            const provider = localStorage.getItem('provider') || 'offline';
            const modelSelect = document.getElementById('modelSelect');
            modelSelect.innerHTML = '';
            const models = providerModels[provider] || [];
            models.forEach(m => {
                const opt = document.createElement('option');
                opt.value = m;
                opt.textContent = m;
                modelSelect.appendChild(opt);
            });
            const savedModel = localStorage.getItem('model');
            if (savedModel && models.includes(savedModel)) {
                modelSelect.value = savedModel;
            }
        }

        function initProviderUI() {
            const providerSelect = document.getElementById('providerSelect');
            const savedProvider = localStorage.getItem('provider') || 'offline';
            providerSelect.value = savedProvider;
            populateModels();

            providerSelect.addEventListener('change', () => {
                localStorage.setItem('provider', providerSelect.value);
                populateModels();
            });
            document.getElementById('modelSelect').addEventListener('change', (e) => {
                localStorage.setItem('model', e.target.value);
            });
        }

        document.addEventListener('DOMContentLoaded', initProviderUI);

        // Helper: build payload hinting provider/model (for future API integration)
        function getAIConfig() {
            return {
                provider: localStorage.getItem('provider') || 'offline',
                model: localStorage.getItem('model') || (providerModels[localStorage.getItem('provider') || 'offline']?.[0] || 'offline-basic')
            };
        }

        // Modify createArticle to include provider/model metadata (UI only for now)

                });
            } else {
                // Fallback: copy to clipboard
                if (navigator.clipboard) {
                    navigator.clipboard.writeText(text).then(() => {
                        alert('تم نسخ النص إلى الحافظة');
                    });
                } else {
                    alert('المشاركة غير مدعومة في هذا المتصفح');
                }
            }
        }

        function generateNew() {
            document.getElementById('searchQuery').value = '';
            document.getElementById('resultSection').classList.remove('show');
            document.getElementById('searchQuery').focus();
        }
    </script>
</body>
</html>
        """.trimIndent()

        webView.loadDataWithBaseURL(null, htmlContent, "text/html", "UTF-8", null)
    }

    // WebAppInterface removed - using web APIs instead

    override fun onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack()
        } else {
            super.onBackPressed()
        }
    }
}
