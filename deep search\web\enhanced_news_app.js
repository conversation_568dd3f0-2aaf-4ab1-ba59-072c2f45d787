/**
 * Enhanced News App JavaScript
 * Handles all frontend functionality for the enhanced news application
 */

const API_BASE = 'http://localhost:8000';
let currentCategory = '';
let currentArticleData = null;

// Initialize the application
document.addEventListener('DOMContentLoaded', function() {
    initializeTabs();
    loadCategories();
    loadImageBackgrounds();
    loadSystemStatus();
    
    // Set up category button handlers
    setupCategoryButtons();
});

// Tab Management
function initializeTabs() {
    const tabLinks = document.querySelectorAll('[data-tab]');
    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetTab = this.getAttribute('data-tab');
            showTab(targetTab);
            
            // Update active tab
            tabLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

function showTab(tabId) {
    // Hide all tabs
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });
    
    // Show target tab
    const targetTab = document.getElementById(tabId);
    if (targetTab) {
        targetTab.classList.add('active');
    }
}

// Category Management
function setupCategoryButtons() {
    const categoryButtons = document.querySelectorAll('.category-btn');
    categoryButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            categoryButtons.forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');
            // Update current category
            currentCategory = this.getAttribute('data-category');
        });
    });
}

async function loadCategories() {
    try {
        const response = await fetch(`${API_BASE}/categories/`);
        const data = await response.json();
        
        if (data.success) {
            displayCategories(data.categories);
            updateCategoryButtons(data.categories);
        }
    } catch (error) {
        console.error('Failed to load categories:', error);
        showError('فشل في تحميل الفئات');
    }
}

function displayCategories(categories) {
    const container = document.getElementById('categoriesList');
    
    if (categories.length === 0) {
        container.innerHTML = '<p>لا توجد فئات مخصصة</p>';
        return;
    }
    
    let html = '<div class="row">';
    categories.forEach(category => {
        html += `
            <div class="col-md-6 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">${category.name_ar} (${category.name_en})</h6>
                        <p class="card-text small">${category.description_ar}</p>
                        <div class="d-flex justify-content-between">
                            <span class="badge bg-${category.is_active ? 'success' : 'secondary'}">
                                ${category.is_active ? 'نشط' : 'غير نشط'}
                            </span>
                            <span class="badge bg-info">${category.category_type}</span>
                        </div>
                        ${category.category_type === 'custom' ? `
                            <button onclick="deleteCategory('${category.id}')" class="btn btn-sm btn-danger mt-2">
                                <i class="fas fa-trash"></i> حذف
                            </button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

function updateCategoryButtons(categories) {
    const container = document.getElementById('categoryButtons');
    let html = '<button class="btn category-btn active" data-category="">جميع الفئات</button>';
    
    categories.forEach(category => {
        if (category.is_active) {
            html += `
                <button class="btn category-btn" data-category="${category.id}">
                    ${category.name_ar}
                </button>
            `;
        }
    });
    
    container.innerHTML = html;
    setupCategoryButtons();
}

async function addCategory() {
    const id = document.getElementById('newCategoryId').value.trim();
    const nameAr = document.getElementById('newCategoryNameAr').value.trim();
    const nameEn = document.getElementById('newCategoryNameEn').value.trim();
    
    if (!id || !nameAr || !nameEn) {
        showError('يرجى ملء جميع الحقول المطلوبة');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/categories/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                id: id,
                name_ar: nameAr,
                name_en: nameEn,
                description_ar: `فئة ${nameAr}`,
                description_en: `${nameEn} category`,
                keywords_ar: [nameAr],
                keywords_en: [nameEn],
                search_terms_ar: [`أخبار ${nameAr}`],
                search_terms_en: [`${nameEn} news`]
            })
        });
        
        const data = await response.json();
        
        if (data.success || response.ok) {
            // Clear form
            document.getElementById('newCategoryId').value = '';
            document.getElementById('newCategoryNameAr').value = '';
            document.getElementById('newCategoryNameEn').value = '';
            
            // Reload categories
            loadCategories();
            showSuccess('تم إضافة الفئة بنجاح');
        } else {
            showError(data.detail || 'فشل في إضافة الفئة');
        }
    } catch (error) {
        console.error('Failed to add category:', error);
        showError('فشل في إضافة الفئة');
    }
}

async function deleteCategory(categoryId) {
    if (!confirm('هل أنت متأكد من حذف هذه الفئة؟')) {
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/categories/${categoryId}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            loadCategories();
            showSuccess('تم حذف الفئة بنجاح');
        } else {
            const data = await response.json();
            showError(data.detail || 'فشل في حذف الفئة');
        }
    } catch (error) {
        console.error('Failed to delete category:', error);
        showError('فشل في حذف الفئة');
    }
}

// Article Generation
async function generateArticle() {
    const topic = document.getElementById('articleTopic').value.trim();
    const style = document.getElementById('articleStyle').value;
    const includeImage = document.getElementById('includeImage').checked;
    const preferCustomImage = document.getElementById('preferCustomImage').checked;
    
    if (!topic) {
        showError('يرجى إدخال موضوع المقال');
        return;
    }
    
    // Show progress
    showProgress();
    updateProgress(1, 'جاري البحث عن المعلومات...');
    
    try {
        // Step 1: Generate article
        updateProgress(2, 'جاري إنشاء المقال...');
        
        const articleResponse = await fetch(`${API_BASE}/publishing/complete-workflow`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                topic: topic,
                article_type: style,
                category: currentCategory,
                publish_to_blogger: false,
                is_draft: true
            })
        });
        
        const articleData = await articleResponse.json();
        
        if (!articleData.success) {
            throw new Error(articleData.error || 'فشل في إنشاء المقال');
        }
        
        currentArticleData = articleData;
        
        // Step 3: Get image if requested
        let imageData = null;
        if (includeImage) {
            updateProgress(3, 'جاري البحث عن الصورة...');
            
            const imageResponse = await fetch(`${API_BASE}/images/article`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    title: articleData.article.article.title,
                    category: currentCategory,
                    prefer_custom: preferCustomImage,
                    keywords: articleData.article.article.seo_keywords
                })
            });
            
            imageData = await imageResponse.json();
        }
        
        // Step 4: Display results
        updateProgress(4, 'تم إنشاء المقال بنجاح!');
        
        setTimeout(() => {
            hideProgress();
            displayArticleResults(articleData, imageData);
        }, 1000);
        
    } catch (error) {
        console.error('Failed to generate article:', error);
        hideProgress();
        showError(error.message || 'فشل في إنشاء المقال');
    }
}

function showProgress() {
    document.getElementById('progressIndicator').style.display = 'block';
    document.getElementById('articleResults').style.display = 'none';
    
    // Reset all steps
    for (let i = 1; i <= 4; i++) {
        const step = document.getElementById(`step${i}`);
        step.classList.remove('active', 'completed');
    }
}

function updateProgress(step, text) {
    // Update text
    document.getElementById('progressText').textContent = text;
    
    // Update steps
    for (let i = 1; i <= 4; i++) {
        const stepElement = document.getElementById(`step${i}`);
        stepElement.classList.remove('active', 'completed');
        
        if (i < step) {
            stepElement.classList.add('completed');
        } else if (i === step) {
            stepElement.classList.add('active');
        }
    }
}

function hideProgress() {
    document.getElementById('progressIndicator').style.display = 'none';
}

function displayArticleResults(articleData, imageData) {
    const container = document.getElementById('articleResults');
    const contentDiv = document.getElementById('articleContent');
    const imageDiv = document.getElementById('articleImage');
    
    // Display article content
    const article = articleData.article.article;
    let html = `
        <h4>${article.title}</h4>
        <div class="article-preview">
            ${article.content.replace(/\n/g, '<br>')}
        </div>
        <div class="mt-3">
            <strong>الكلمات المفتاحية:</strong> ${article.seo_keywords.join(', ')}
        </div>
        <div class="mt-2">
            <strong>عدد الكلمات:</strong> ${article.word_count}
        </div>
    `;
    
    contentDiv.innerHTML = html;
    
    // Display image if available
    if (imageData && imageData.success) {
        let imageHtml = '';
        
        if (imageData.image_type === 'custom_generated' || imageData.image_type === 'custom_fallback') {
            imageHtml = `
                <div class="mt-3">
                    <h5>الصورة المخصصة:</h5>
                    <img src="data:image/jpeg;base64,${imageData.image_base64}" class="image-preview" alt="${article.title}">
                </div>
            `;
        } else if (imageData.image) {
            imageHtml = `
                <div class="mt-3">
                    <h5>الصورة من ${imageData.provider}:</h5>
                    <img src="${imageData.image.url}" class="image-preview" alt="${imageData.image.title}">
                    <p class="small">المصور: ${imageData.image.photographer}</p>
                </div>
            `;
        }
        
        imageDiv.innerHTML = imageHtml;
    } else {
        imageDiv.innerHTML = '';
    }
    
    container.style.display = 'block';
}

// Image Management
async function loadImageBackgrounds() {
    try {
        const response = await fetch(`${API_BASE}/images/backgrounds`);
        const data = await response.json();
        
        if (data.success) {
            const select = document.getElementById('imageBackground');
            select.innerHTML = '<option value="">خلفية عشوائية</option>';
            
            data.backgrounds.forEach(bg => {
                select.innerHTML += `<option value="${bg}">${bg}</option>`;
            });
        }
    } catch (error) {
        console.error('Failed to load backgrounds:', error);
    }
}

async function searchImages() {
    const query = document.getElementById('imageSearchQuery').value.trim();
    const provider = document.getElementById('imageProvider').value;
    
    if (!query) {
        showError('يرجى إدخال كلمة البحث');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/images/search`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                query: query,
                provider: provider || null,
                per_page: 12
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayImageResults(data.images, data.provider_used);
        } else {
            showError(data.error || 'فشل في البحث عن الصور');
        }
    } catch (error) {
        console.error('Failed to search images:', error);
        showError('فشل في البحث عن الصور');
    }
}

async function generateCustomImage() {
    const title = document.getElementById('customImageTitle').value.trim();
    const background = document.getElementById('imageBackground').value;
    
    if (!title) {
        showError('يرجى إدخال عنوان الصورة');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/images/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title: title,
                background: background || null,
                style: 'auto'
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            const resultsDiv = document.getElementById('imageResults');
            resultsDiv.innerHTML = `
                <div class="result-section">
                    <h5>الصورة المخصصة:</h5>
                    <img src="data:image/jpeg;base64,${data.image_base64}" class="image-preview" alt="${title}">
                    <p>الخلفية المستخدمة: ${data.background_used}</p>
                </div>
            `;
        } else {
            showError(data.error || 'فشل في إنشاء الصورة');
        }
    } catch (error) {
        console.error('Failed to generate custom image:', error);
        showError('فشل في إنشاء الصورة');
    }
}

function displayImageResults(images, provider) {
    const container = document.getElementById('imageResults');
    
    if (!images || images.length === 0) {
        container.innerHTML = '<p>لم يتم العثور على صور</p>';
        return;
    }
    
    let html = `<div class="result-section"><h5>نتائج البحث من ${provider}:</h5><div class="row">`;
    
    images.forEach(image => {
        html += `
            <div class="col-md-3 mb-3">
                <div class="card">
                    <img src="${image.thumbnail}" class="card-img-top" alt="${image.title}">
                    <div class="card-body p-2">
                        <p class="card-text small">${image.title || 'بدون عنوان'}</p>
                        <p class="card-text small text-muted">المصور: ${image.photographer}</p>
                        <a href="${image.url}" target="_blank" class="btn btn-sm btn-primary">عرض</a>
                    </div>
                </div>
            </div>
        `;
    });
    
    html += '</div></div>';
    container.innerHTML = html;
}

// Settings Management
async function saveImageAPISettings() {
    const unsplashKey = document.getElementById('unsplashApiKey').value.trim();
    const pixabayKey = document.getElementById('pixabayApiKey').value.trim();
    const pexelsKey = document.getElementById('pexelsApiKey').value.trim();
    
    const promises = [];
    
    if (unsplashKey) {
        promises.push(configureProvider('unsplash', unsplashKey));
    }
    if (pixabayKey) {
        promises.push(configureProvider('pixabay', pixabayKey));
    }
    if (pexelsKey) {
        promises.push(configureProvider('pexels', pexelsKey));
    }
    
    if (promises.length === 0) {
        showError('يرجى إدخال مفتاح API واحد على الأقل');
        return;
    }
    
    try {
        await Promise.all(promises);
        showSuccess('تم حفظ إعدادات الصور بنجاح');
        loadSystemStatus();
    } catch (error) {
        showError('فشل في حفظ بعض الإعدادات');
    }
}

async function configureProvider(provider, apiKey) {
    const response = await fetch(`${API_BASE}/images/configure`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            provider: provider,
            api_key: apiKey
        })
    });
    
    if (!response.ok) {
        throw new Error(`Failed to configure ${provider}`);
    }
}

async function saveBloggerSettings() {
    const blogId = document.getElementById('blogId').value.trim();
    const serviceAccountJson = document.getElementById('serviceAccountJson').value.trim();
    
    if (!blogId || !serviceAccountJson) {
        showError('يرجى ملء جميع حقول إعدادات بلوجر');
        return;
    }
    
    try {
        const response = await fetch(`${API_BASE}/publishing/blogger/configure`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                service_account_json: serviceAccountJson,
                blog_id: blogId
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showSuccess('تم حفظ إعدادات بلوجر بنجاح');
            loadSystemStatus();
        } else {
            showError(data.error || 'فشل في حفظ إعدادات بلوجر');
        }
    } catch (error) {
        console.error('Failed to save blogger settings:', error);
        showError('فشل في حفظ إعدادات بلوجر');
    }
}

async function loadSystemStatus() {
    try {
        const [imageStatus, publishingStatus] = await Promise.all([
            fetch(`${API_BASE}/images/status`),
            fetch(`${API_BASE}/publishing/blogger/status`)
        ]);
        
        const imageData = await imageStatus.json();
        const publishingData = await publishingStatus.json();
        
        displaySystemStatus(imageData, publishingData);
    } catch (error) {
        console.error('Failed to load system status:', error);
        document.getElementById('systemStatus').innerHTML = '<p>فشل في تحميل حالة النظام</p>';
    }
}

function displaySystemStatus(imageData, publishingData) {
    const container = document.getElementById('systemStatus');
    
    let html = '<h5>حالة النظام</h5>';
    
    // Image providers status
    if (imageData.success) {
        html += '<h6>مزودي الصور:</h6><ul>';
        Object.entries(imageData.status.providers).forEach(([provider, status]) => {
            const icon = status.configured ? '✅' : '❌';
            html += `<li>${icon} ${status.name}: ${status.configured ? 'مُكوَّن' : 'غير مُكوَّن'}</li>`;
        });
        html += '</ul>';
        
        html += `<h6>مولد الصور المخصص:</h6>`;
        html += `<p>✅ متاح - ${imageData.status.custom_generator.stats.backgrounds_available} خلفية متاحة</p>`;
    }
    
    // Publishing status
    if (publishingData.success) {
        html += '<h6>نشر بلوجر:</h6>';
        const icon = publishingData.status.ready ? '✅' : '❌';
        html += `<p>${icon} ${publishingData.status.ready ? 'جاهز للنشر' : 'غير مُكوَّن'}</p>`;
        
        if (publishingData.status.blog_name) {
            html += `<p>المدونة: ${publishingData.status.blog_name}</p>`;
        }
    }
    
    container.innerHTML = html;
}

// Publishing Functions
async function publishToBlogger() {
    if (!currentArticleData) {
        showError('لا يوجد مقال للنشر');
        return;
    }
    
    if (!confirm('هل تريد نشر المقال على بلوجر؟')) {
        return;
    }
    
    try {
        const article = currentArticleData.article.article;
        
        const response = await fetch(`${API_BASE}/publishing/blogger/publish`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                title: article.title,
                content: article.content,
                labels: article.seo_keywords,
                is_draft: false,
                meta_description: article.meta_description,
                seo_keywords: article.seo_keywords
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showSuccess(`تم نشر المقال بنجاح! <a href="${data.post_url}" target="_blank">عرض المقال</a>`);
        } else {
            showError(data.error || 'فشل في نشر المقال');
        }
    } catch (error) {
        console.error('Failed to publish to blogger:', error);
        showError('فشل في نشر المقال');
    }
}

function copyHTML() {
    if (!currentArticleData) {
        showError('لا يوجد مقال للنسخ');
        return;
    }
    
    const htmlContent = currentArticleData.html_copy;
    
    navigator.clipboard.writeText(htmlContent).then(() => {
        showSuccess('تم نسخ HTML بنجاح');
    }).catch(() => {
        showError('فشل في نسخ HTML');
    });
}

function downloadArticle() {
    if (!currentArticleData) {
        showError('لا يوجد مقال للتحميل');
        return;
    }
    
    const article = currentArticleData.article.article;
    const content = `${article.title}\n\n${article.content}`;
    
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    const a = document.createElement('a');
    a.href = url;
    a.download = `${article.title}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// Utility Functions
function showError(message) {
    const errorDiv = document.getElementById('errorDisplay');
    const messageDiv = document.getElementById('errorMessage');
    
    messageDiv.innerHTML = message;
    errorDiv.style.display = 'block';
    
    setTimeout(() => {
        errorDiv.style.display = 'none';
    }, 5000);
}

function showSuccess(message) {
    // Create a temporary success message
    const successDiv = document.createElement('div');
    successDiv.className = 'alert alert-success alert-dismissible fade show';
    successDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.querySelector('.main-container').insertBefore(successDiv, document.querySelector('.main-container').firstChild);
    
    setTimeout(() => {
        successDiv.remove();
    }, 5000);
}
