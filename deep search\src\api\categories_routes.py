"""
News Categories API Routes
Provides endpoints for managing news categories
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
from loguru import logger

from ..news.news_categories import NewsCategoriesManager, NewsCategory, CategoryType


router = APIRouter(prefix="/categories", tags=["categories"])

# Global categories manager instance
categories_manager = NewsCategoriesManager()


class CategoryResponse(BaseModel):
    """Category response model"""
    id: str
    name_ar: str
    name_en: str
    description_ar: str
    description_en: str
    keywords_ar: List[str]
    keywords_en: List[str]
    search_terms_ar: List[str]
    search_terms_en: List[str]
    category_type: str
    is_active: bool
    priority: int
    color: str
    icon: str
    custom_prompts: Optional[Dict[str, str]] = None
    seo_keywords: Optional[List[str]] = None


class CreateCategoryRequest(BaseModel):
    """Create category request model"""
    id: str
    name_ar: str
    name_en: str
    description_ar: str
    description_en: str
    keywords_ar: List[str]
    keywords_en: List[str]
    search_terms_ar: List[str]
    search_terms_en: List[str]
    priority: int = 1
    color: str = "#007bff"
    icon: str = "fas fa-newspaper"
    custom_prompts: Optional[Dict[str, str]] = None
    seo_keywords: Optional[List[str]] = None


class UpdateCategoryRequest(BaseModel):
    """Update category request model"""
    name_ar: Optional[str] = None
    name_en: Optional[str] = None
    description_ar: Optional[str] = None
    description_en: Optional[str] = None
    keywords_ar: Optional[List[str]] = None
    keywords_en: Optional[List[str]] = None
    search_terms_ar: Optional[List[str]] = None
    search_terms_en: Optional[List[str]] = None
    is_active: Optional[bool] = None
    priority: Optional[int] = None
    color: Optional[str] = None
    icon: Optional[str] = None
    custom_prompts: Optional[Dict[str, str]] = None
    seo_keywords: Optional[List[str]] = None


class CategoriesListResponse(BaseModel):
    """Categories list response model"""
    success: bool
    categories: List[CategoryResponse]
    total_count: int
    active_count: int


class CategoryStatsResponse(BaseModel):
    """Category statistics response model"""
    success: bool
    stats: Dict[str, Any]


def get_categories_manager() -> NewsCategoriesManager:
    """Get categories manager instance"""
    return categories_manager


def category_to_response(category: NewsCategory) -> CategoryResponse:
    """Convert NewsCategory to CategoryResponse"""
    return CategoryResponse(
        id=category.id,
        name_ar=category.name_ar,
        name_en=category.name_en,
        description_ar=category.description_ar,
        description_en=category.description_en,
        keywords_ar=category.keywords_ar,
        keywords_en=category.keywords_en,
        search_terms_ar=category.search_terms_ar,
        search_terms_en=category.search_terms_en,
        category_type=category.category_type.value,
        is_active=category.is_active,
        priority=category.priority,
        color=category.color,
        icon=category.icon,
        custom_prompts=category.custom_prompts,
        seo_keywords=category.seo_keywords
    )


@router.get("/", response_model=CategoriesListResponse)
async def get_all_categories(
    active_only: bool = True,
    manager: NewsCategoriesManager = Depends(get_categories_manager)
):
    """
    Get all news categories
    
    Returns a list of all available news categories with their configurations.
    """
    try:
        categories = manager.get_all_categories(active_only=active_only)
        
        category_responses = [category_to_response(cat) for cat in categories]
        
        return CategoriesListResponse(
            success=True,
            categories=category_responses,
            total_count=len(category_responses),
            active_count=len([cat for cat in category_responses if cat.is_active])
        )
        
    except Exception as e:
        logger.error(f"Failed to get categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{category_id}", response_model=CategoryResponse)
async def get_category(
    category_id: str,
    manager: NewsCategoriesManager = Depends(get_categories_manager)
):
    """
    Get a specific category by ID
    
    Returns detailed information about a specific news category.
    """
    try:
        category = manager.get_category(category_id)
        
        if not category:
            raise HTTPException(status_code=404, detail=f"Category '{category_id}' not found")
        
        return category_to_response(category)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get category {category_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=CategoryResponse)
async def create_custom_category(
    request: CreateCategoryRequest,
    manager: NewsCategoriesManager = Depends(get_categories_manager)
):
    """
    Create a new custom category
    
    Creates a new custom news category with the provided configuration.
    """
    try:
        # Check if category already exists
        if manager.get_category(request.id):
            raise HTTPException(status_code=400, detail=f"Category '{request.id}' already exists")
        
        # Create new category
        new_category = NewsCategory(
            id=request.id,
            name_ar=request.name_ar,
            name_en=request.name_en,
            description_ar=request.description_ar,
            description_en=request.description_en,
            keywords_ar=request.keywords_ar,
            keywords_en=request.keywords_en,
            search_terms_ar=request.search_terms_ar,
            search_terms_en=request.search_terms_en,
            category_type=CategoryType.CUSTOM,
            priority=request.priority,
            color=request.color,
            icon=request.icon,
            custom_prompts=request.custom_prompts,
            seo_keywords=request.seo_keywords
        )
        
        success = manager.add_custom_category(new_category)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to create category")
        
        return category_to_response(new_category)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create category: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{category_id}", response_model=CategoryResponse)
async def update_category(
    category_id: str,
    request: UpdateCategoryRequest,
    manager: NewsCategoriesManager = Depends(get_categories_manager)
):
    """
    Update an existing category
    
    Updates the configuration of an existing news category.
    """
    try:
        # Check if category exists
        category = manager.get_category(category_id)
        if not category:
            raise HTTPException(status_code=404, detail=f"Category '{category_id}' not found")
        
        # Prepare updates
        updates = {}
        for field, value in request.dict(exclude_unset=True).items():
            if value is not None:
                updates[field] = value
        
        success = manager.update_category(category_id, updates)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to update category")
        
        # Get updated category
        updated_category = manager.get_category(category_id)
        return category_to_response(updated_category)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update category {category_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{category_id}")
async def delete_custom_category(
    category_id: str,
    manager: NewsCategoriesManager = Depends(get_categories_manager)
):
    """
    Delete a custom category
    
    Deletes a custom news category. Default categories cannot be deleted.
    """
    try:
        # Check if category exists
        category = manager.get_category(category_id)
        if not category:
            raise HTTPException(status_code=404, detail=f"Category '{category_id}' not found")
        
        # Check if it's a custom category
        if category.category_type != CategoryType.CUSTOM:
            raise HTTPException(status_code=400, detail="Cannot delete default categories")
        
        success = manager.delete_custom_category(category_id)
        
        if not success:
            raise HTTPException(status_code=500, detail="Failed to delete category")
        
        return {"success": True, "message": f"Category '{category_id}' deleted successfully"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete category {category_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/search/{query}")
async def search_categories(
    query: str,
    language: str = "ar",
    manager: NewsCategoriesManager = Depends(get_categories_manager)
):
    """
    Search categories by name or keywords
    
    Searches for categories that match the provided query in their names or keywords.
    """
    try:
        categories = manager.search_categories(query, language)
        
        category_responses = [category_to_response(cat) for cat in categories]
        
        return {
            "success": True,
            "query": query,
            "language": language,
            "categories": category_responses,
            "total_found": len(category_responses)
        }
        
    except Exception as e:
        logger.error(f"Failed to search categories: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/stats/overview", response_model=CategoryStatsResponse)
async def get_category_stats(
    manager: NewsCategoriesManager = Depends(get_categories_manager)
):
    """
    Get category statistics
    
    Returns statistics about the available news categories.
    """
    try:
        stats = manager.get_category_stats()
        
        return CategoryStatsResponse(
            success=True,
            stats=stats
        )
        
    except Exception as e:
        logger.error(f"Failed to get category stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))
