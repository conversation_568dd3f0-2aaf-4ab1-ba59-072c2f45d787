#!/usr/bin/env python3
"""
Setup script to create necessary directories for DeepSearch Enhanced News App
"""

import os
from pathlib import Path

def create_directories():
    """Create all necessary directories"""
    
    base_dir = Path(__file__).parent
    
    directories = [
        "data/backgrounds",
        "data/fonts", 
        "data/generated_images",
        "config",
        "logs"
    ]
    
    print("🔧 Setting up directories for DeepSearch Enhanced News App...")
    
    for directory in directories:
        dir_path = base_dir / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ Created: {directory}")
    
    print("\n📁 Directory structure created successfully!")
    print("🚀 You can now run: python run_server.py")

if __name__ == "__main__":
    create_directories()
