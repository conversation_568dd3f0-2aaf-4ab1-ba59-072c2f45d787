"""
Image providers for fetching free licensed images
"""

import aiohttp
import asyncio
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod
from loguru import logger
import json
from pathlib import Path


class BaseImageProvider(ABC):
    """Base class for image providers"""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()
    
    @abstractmethod
    async def search_images(
        self,
        query: str,
        per_page: int = 10,
        orientation: str = "all",
        category: str = None
    ) -> Dict[str, Any]:
        """Search for images"""
        pass
    
    @abstractmethod
    def is_configured(self) -> bool:
        """Check if provider is properly configured"""
        pass
    
    @abstractmethod
    def get_provider_name(self) -> str:
        """Get provider name"""
        pass


class UnsplashProvider(BaseImageProvider):
    """Unsplash image provider"""
    
    def __init__(self, api_key: Optional[str] = None):
        super().__init__(api_key)
        self.base_url = "https://api.unsplash.com"
    
    def is_configured(self) -> bool:
        return self.api_key is not None
    
    def get_provider_name(self) -> str:
        return "Unsplash"
    
    async def search_images(
        self,
        query: str,
        per_page: int = 10,
        orientation: str = "all",
        category: str = None
    ) -> Dict[str, Any]:
        """Search for images on Unsplash"""
        if not self.is_configured():
            return {
                "success": False,
                "error": "Unsplash API key not configured"
            }
        
        try:
            headers = {
                "Authorization": f"Client-ID {self.api_key}",
                "Accept-Version": "v1"
            }
            
            params = {
                "query": query,
                "per_page": min(per_page, 30),  # Unsplash max is 30
                "orientation": orientation if orientation != "all" else None
            }
            
            # Remove None values
            params = {k: v for k, v in params.items() if v is not None}
            
            url = f"{self.base_url}/search/photos"
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    images = []
                    for photo in data.get("results", []):
                        images.append({
                            "id": photo["id"],
                            "title": photo.get("description") or photo.get("alt_description", ""),
                            "url": photo["urls"]["regular"],
                            "thumbnail": photo["urls"]["thumb"],
                            "full_size": photo["urls"]["full"],
                            "raw": photo["urls"]["raw"],
                            "width": photo["width"],
                            "height": photo["height"],
                            "photographer": photo["user"]["name"],
                            "photographer_url": photo["user"]["links"]["html"],
                            "download_url": photo["links"]["download_location"],
                            "source": "Unsplash",
                            "license": "Unsplash License",
                            "license_url": "https://unsplash.com/license"
                        })
                    
                    return {
                        "success": True,
                        "provider": "Unsplash",
                        "query": query,
                        "total_results": data.get("total", 0),
                        "images": images
                    }
                else:
                    error_data = await response.json()
                    return {
                        "success": False,
                        "error": f"Unsplash API error: {error_data.get('errors', [response.status])}"
                    }
                    
        except Exception as e:
            logger.error(f"Unsplash search error: {str(e)}")
            return {
                "success": False,
                "error": f"Unsplash search failed: {str(e)}"
            }


class PixabayProvider(BaseImageProvider):
    """Pixabay image provider"""
    
    def __init__(self, api_key: Optional[str] = None):
        super().__init__(api_key)
        self.base_url = "https://pixabay.com/api/"
    
    def is_configured(self) -> bool:
        return self.api_key is not None
    
    def get_provider_name(self) -> str:
        return "Pixabay"
    
    async def search_images(
        self,
        query: str,
        per_page: int = 10,
        orientation: str = "all",
        category: str = None
    ) -> Dict[str, Any]:
        """Search for images on Pixabay"""
        if not self.is_configured():
            return {
                "success": False,
                "error": "Pixabay API key not configured"
            }
        
        try:
            params = {
                "key": self.api_key,
                "q": query,
                "image_type": "photo",
                "per_page": min(per_page, 200),  # Pixabay max is 200
                "safesearch": "true",
                "order": "popular"
            }
            
            if orientation != "all":
                params["orientation"] = orientation
            
            if category:
                params["category"] = category
            
            async with self.session.get(self.base_url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    images = []
                    for photo in data.get("hits", []):
                        images.append({
                            "id": str(photo["id"]),
                            "title": photo.get("tags", ""),
                            "url": photo["webformatURL"],
                            "thumbnail": photo["previewURL"],
                            "full_size": photo["fullHDURL"] or photo["largeImageURL"],
                            "raw": photo["fullHDURL"] or photo["largeImageURL"],
                            "width": photo["imageWidth"],
                            "height": photo["imageHeight"],
                            "photographer": photo["user"],
                            "photographer_url": f"https://pixabay.com/users/{photo['user']}-{photo['user_id']}/",
                            "download_url": photo["webformatURL"],
                            "source": "Pixabay",
                            "license": "Pixabay License",
                            "license_url": "https://pixabay.com/service/license/"
                        })
                    
                    return {
                        "success": True,
                        "provider": "Pixabay",
                        "query": query,
                        "total_results": data.get("total", 0),
                        "images": images
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Pixabay API error: {response.status}"
                    }
                    
        except Exception as e:
            logger.error(f"Pixabay search error: {str(e)}")
            return {
                "success": False,
                "error": f"Pixabay search failed: {str(e)}"
            }


class PexelsProvider(BaseImageProvider):
    """Pexels image provider"""
    
    def __init__(self, api_key: Optional[str] = None):
        super().__init__(api_key)
        self.base_url = "https://api.pexels.com/v1"
    
    def is_configured(self) -> bool:
        return self.api_key is not None
    
    def get_provider_name(self) -> str:
        return "Pexels"
    
    async def search_images(
        self,
        query: str,
        per_page: int = 10,
        orientation: str = "all",
        category: str = None
    ) -> Dict[str, Any]:
        """Search for images on Pexels"""
        if not self.is_configured():
            return {
                "success": False,
                "error": "Pexels API key not configured"
            }
        
        try:
            headers = {
                "Authorization": self.api_key
            }
            
            params = {
                "query": query,
                "per_page": min(per_page, 80),  # Pexels max is 80
                "order": "popular"
            }
            
            if orientation != "all":
                params["orientation"] = orientation
            
            url = f"{self.base_url}/search"
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    images = []
                    for photo in data.get("photos", []):
                        images.append({
                            "id": str(photo["id"]),
                            "title": photo.get("alt", ""),
                            "url": photo["src"]["large"],
                            "thumbnail": photo["src"]["small"],
                            "full_size": photo["src"]["original"],
                            "raw": photo["src"]["original"],
                            "width": photo["width"],
                            "height": photo["height"],
                            "photographer": photo["photographer"],
                            "photographer_url": photo["photographer_url"],
                            "download_url": photo["src"]["original"],
                            "source": "Pexels",
                            "license": "Pexels License",
                            "license_url": "https://www.pexels.com/license/"
                        })
                    
                    return {
                        "success": True,
                        "provider": "Pexels",
                        "query": query,
                        "total_results": data.get("total_results", 0),
                        "images": images
                    }
                else:
                    return {
                        "success": False,
                        "error": f"Pexels API error: {response.status}"
                    }
                    
        except Exception as e:
            logger.error(f"Pexels search error: {str(e)}")
            return {
                "success": False,
                "error": f"Pexels search failed: {str(e)}"
            }
