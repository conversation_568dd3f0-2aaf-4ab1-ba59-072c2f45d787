#!/usr/bin/env python3
"""
Quick Test Tool for DeepSearch Agent Pro
Fast validation of core components
"""

import asyncio
import sys
import time
from pathlib import Path
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.utils.config_loader import load_config
from src.utils.logger import setup_logging
from loguru import logger


class QuickTester:
    """Quick validation tool for core components"""
    
    def __init__(self):
        self.config = load_config()
        self.results = {}
        
    def print_header(self):
        """Print test header"""
        print("""
\033[96m╔══════════════════════════════════════════════════════════════╗
║                    ⚡ Quick Test Tool ⚡                     ║
║                                                              ║
║  \033[93mاختبار سريع للمكونات الأساسية في النظام\033[96m                ║
║                                                              ║
╚══════════════════════════════════════════════════════════════╝\033[0m
        """)
    
    async def test_imports(self):
        """Test if all modules can be imported"""
        logger.info("🔍 Testing module imports...")
        
        modules_to_test = [
            ("src.core.search_engine", "DeepSearchEngine"),
            ("src.ai.ai_manager", "AIManager"),
            ("src.ai.article_generator", "ArticleGenerator"),
            ("src.seo.seo_optimizer", "SEOOptimizer"),
            ("src.seo.advanced_keyword_extractor", "AdvancedKeywordExtractor"),
            ("src.localization.language_manager", "LanguageManager"),
            ("src.news.auto_news_discovery", "AutoNewsDiscovery"),
            ("src.publishing.blogger_publisher", "BloggerPublisher"),
            ("src.scrapers.scraper_manager", "ScraperManager"),
            ("src.scrapers.enhanced_search_apis", "EnhancedSearchManager")
        ]
        
        import_results = {}
        
        for module_name, class_name in modules_to_test:
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                import_results[f"{module_name}.{class_name}"] = "✅ OK"
                logger.success(f"✅ {module_name}.{class_name}")
            except Exception as e:
                import_results[f"{module_name}.{class_name}"] = f"❌ {str(e)}"
                logger.error(f"❌ {module_name}.{class_name}: {str(e)}")
        
        self.results["imports"] = import_results
        return all("✅" in result for result in import_results.values())
    
    async def test_configuration(self):
        """Test configuration loading"""
        logger.info("⚙️ Testing configuration...")
        
        try:
            config = load_config()
            
            # Check required sections
            required_sections = ["api", "ai", "search", "database"]
            config_status = {}
            
            for section in required_sections:
                if section in config:
                    config_status[section] = "✅ Present"
                else:
                    config_status[section] = "⚠️ Missing"
            
            # Check AI configuration
            ai_config = config.get("ai", {})
            gemini_config = ai_config.get("gemini", {})
            
            if gemini_config.get("api_key"):
                config_status["gemini_api_key"] = "✅ Configured"
            else:
                config_status["gemini_api_key"] = "⚠️ Not configured"
            
            self.results["configuration"] = config_status
            logger.success("✅ Configuration loaded successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Configuration failed: {str(e)}")
            self.results["configuration"] = {"error": str(e)}
            return False
    
    async def test_search_engine(self):
        """Test search engine initialization"""
        logger.info("🔍 Testing search engine...")
        
        try:
            from src.core.search_engine import DeepSearchEngine
            
            async with DeepSearchEngine(self.config) as engine:
                # Test health check
                health = await engine.health_check()
                
                search_results = {
                    "initialization": "✅ OK",
                    "health_status": health.get("overall_status", "unknown"),
                    "components": {}
                }
                
                # Test individual components
                if hasattr(engine, 'scraper_manager'):
                    search_results["components"]["scraper_manager"] = "✅ OK"
                
                if hasattr(engine, 'ai_manager'):
                    search_results["components"]["ai_manager"] = "✅ OK"
                
                if hasattr(engine, 'database_manager'):
                    search_results["components"]["database_manager"] = "✅ OK"
                
                if hasattr(engine, 'auto_news_discovery'):
                    search_results["components"]["auto_news_discovery"] = "✅ OK"
                
                self.results["search_engine"] = search_results
                logger.success("✅ Search engine initialized successfully")
                return True
                
        except Exception as e:
            logger.error(f"❌ Search engine failed: {str(e)}")
            self.results["search_engine"] = {"error": str(e)}
            return False
    
    async def test_ai_components(self):
        """Test AI components"""
        logger.info("🤖 Testing AI components...")
        
        try:
            from src.ai.ai_manager import AIManager
            from src.ai.article_generator import ArticleGenerator
            
            # Test AI Manager
            ai_manager = AIManager(self.config)
            ai_status = ai_manager.get_status()
            
            # Test Article Generator
            article_generator = ArticleGenerator(self.config)
            generator_status = article_generator.get_status()
            
            ai_results = {
                "ai_manager": "✅ OK",
                "article_generator": "✅ OK",
                "gemini_configured": "✅ Yes" if generator_status.get("configured") else "⚠️ No",
                "providers": ai_status
            }
            
            self.results["ai_components"] = ai_results
            logger.success("✅ AI components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ AI components failed: {str(e)}")
            self.results["ai_components"] = {"error": str(e)}
            return False
    
    async def test_seo_components(self):
        """Test SEO components"""
        logger.info("📊 Testing SEO components...")
        
        try:
            from src.seo.seo_optimizer import SEOOptimizer
            from src.seo.advanced_keyword_extractor import AdvancedKeywordExtractor
            
            # Test SEO Optimizer
            seo_optimizer = SEOOptimizer(self.config)
            
            # Test Advanced Keyword Extractor
            keyword_extractor = AdvancedKeywordExtractor(self.config)
            algorithms = keyword_extractor.get_available_algorithms()
            
            seo_results = {
                "seo_optimizer": "✅ OK",
                "keyword_extractor": "✅ OK",
                "available_algorithms": algorithms,
                "algorithm_count": len(algorithms)
            }
            
            self.results["seo_components"] = seo_results
            logger.success(f"✅ SEO components OK ({len(algorithms)} algorithms available)")
            return True
            
        except Exception as e:
            logger.error(f"❌ SEO components failed: {str(e)}")
            self.results["seo_components"] = {"error": str(e)}
            return False
    
    async def test_language_support(self):
        """Test language support"""
        logger.info("🌍 Testing language support...")
        
        try:
            from src.localization.language_manager import LanguageManager
            
            language_manager = LanguageManager(self.config)
            
            # Test basic functionality
            supported_languages = language_manager.get_supported_languages()
            supported_dialects = language_manager.get_supported_dialects()
            
            # Test dialect conversion
            test_text = "هذا اختبار للنظام"
            egyptian_text = language_manager.apply_dialect(test_text, "egyptian")
            
            language_results = {
                "language_manager": "✅ OK",
                "supported_languages": len(supported_languages),
                "supported_dialects": len(supported_dialects),
                "dialect_conversion": "✅ Working" if egyptian_text != test_text else "⚠️ No change"
            }
            
            self.results["language_support"] = language_results
            logger.success(f"✅ Language support OK ({len(supported_languages)} languages, {len(supported_dialects)} dialects)")
            return True
            
        except Exception as e:
            logger.error(f"❌ Language support failed: {str(e)}")
            self.results["language_support"] = {"error": str(e)}
            return False
    
    async def test_enhanced_apis(self):
        """Test enhanced search APIs"""
        logger.info("🚀 Testing enhanced APIs...")
        
        try:
            from src.scrapers.enhanced_search_apis import EnhancedSearchManager
            from src.news.auto_news_discovery import AutoNewsDiscovery
            
            # Test Enhanced Search Manager
            search_manager = EnhancedSearchManager(self.config)
            search_status = search_manager.get_status()
            
            # Test Auto News Discovery
            news_discovery = AutoNewsDiscovery(self.config)
            news_status = news_discovery.get_status()
            
            api_results = {
                "enhanced_search_manager": "✅ OK",
                "auto_news_discovery": "✅ OK",
                "search_apis_configured": search_status.get("total_configured", 0),
                "news_apis_configured": news_status.get("configured_apis", 0),
                "available_categories": len(news_status.get("available_categories", []))
            }
            
            self.results["enhanced_apis"] = api_results
            logger.success("✅ Enhanced APIs initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Enhanced APIs failed: {str(e)}")
            self.results["enhanced_apis"] = {"error": str(e)}
            return False
    
    def print_summary(self):
        """Print test summary"""
        total_tests = len(self.results)
        passed_tests = sum(1 for result in self.results.values() if not isinstance(result, dict) or "error" not in result)
        
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        print(f"""
\033[96m╔══════════════════════════════════════════════════════════════╗
║                    📊 Quick Test Summary                     ║
╠══════════════════════════════════════════════════════════════╣
║ Tests Run:      {total_tests:2d}                                        ║
║ Passed:         {passed_tests:2d}                                        ║
║ Success Rate:   {success_rate:5.1f}%                                 ║
╚══════════════════════════════════════════════════════════════╝\033[0m
        """)
        
        # Print detailed results
        print("\n\033[93m📋 Detailed Results:\033[0m")
        for test_name, result in self.results.items():
            if isinstance(result, dict) and "error" in result:
                print(f"❌ {test_name}: {result['error']}")
            else:
                print(f"✅ {test_name}: OK")
        
        print(f"\n\033[92m🎯 System Status: {'READY' if success_rate >= 80 else 'NEEDS ATTENTION'}\033[0m")
        
        if success_rate < 80:
            print("\n\033[93m💡 Recommendations:\033[0m")
            print("• Run full test suite: python test_runner.py")
            print("• Check configuration: config/config.yaml")
            print("• Install missing dependencies: pip install -r requirements.txt")
            print("• Configure API keys in the web interface")
    
    async def run_quick_tests(self):
        """Run all quick tests"""
        self.print_header()
        
        start_time = time.time()
        
        tests = [
            ("Module Imports", self.test_imports),
            ("Configuration", self.test_configuration),
            ("Search Engine", self.test_search_engine),
            ("AI Components", self.test_ai_components),
            ("SEO Components", self.test_seo_components),
            ("Language Support", self.test_language_support),
            ("Enhanced APIs", self.test_enhanced_apis)
        ]
        
        logger.info("🚀 Starting quick validation tests...")
        
        for test_name, test_func in tests:
            try:
                await test_func()
            except Exception as e:
                logger.error(f"❌ {test_name} failed: {str(e)}")
                self.results[test_name.lower().replace(" ", "_")] = {"error": str(e)}
        
        total_time = time.time() - start_time
        logger.info(f"⏱️ Tests completed in {total_time:.2f} seconds")
        
        self.print_summary()


async def main():
    """Main function"""
    setup_logging({})
    
    tester = QuickTester()
    
    try:
        await tester.run_quick_tests()
    except KeyboardInterrupt:
        logger.info("Tests interrupted by user")
    except Exception as e:
        logger.error(f"Quick test failed: {str(e)}")


if __name__ == "__main__":
    asyncio.run(main())
