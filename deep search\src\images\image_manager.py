"""
Main image manager that coordinates all image operations
"""

import asyncio
from typing import Dict, List, Optional, Any, Union
from loguru import logger
from pathlib import Path

from .image_providers import Un<PERSON>lash<PERSON>rovider, <PERSON>xabayProvider, PexelsProvider
from .image_generator import CustomImageGenerator


class ImageManager:
    """Main image manager for handling all image operations"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        
        # Initialize providers
        self.providers = {}
        self._init_providers()
        
        # Initialize custom generator
        self.custom_generator = CustomImageGenerator()
        
        # Settings
        self.default_provider = "unsplash"
        self.fallback_providers = ["pixabay", "pexels"]
        self.auto_fallback = True
    
    def _init_providers(self):
        """Initialize image providers"""
        # Get API keys from config
        image_config = self.config.get('images', {})
        
        self.providers = {
            'unsplash': UnsplashProvider(
                api_key=image_config.get('unsplash_api_key')
            ),
            'pixabay': PixabayProvider(
                api_key=image_config.get('pixabay_api_key')
            ),
            'pexels': PexelsProvider(
                api_key=image_config.get('pexels_api_key')
            )
        }
    
    def configure_provider(self, provider_name: str, api_key: str) -> bool:
        """Configure a specific provider with API key"""
        try:
            if provider_name not in self.providers:
                logger.error(f"Unknown provider: {provider_name}")
                return False
            
            # Update provider with new API key
            if provider_name == 'unsplash':
                self.providers[provider_name] = UnsplashProvider(api_key)
            elif provider_name == 'pixabay':
                self.providers[provider_name] = PixabayProvider(api_key)
            elif provider_name == 'pexels':
                self.providers[provider_name] = PexelsProvider(api_key)
            
            logger.info(f"Configured {provider_name} provider")
            return True
            
        except Exception as e:
            logger.error(f"Failed to configure {provider_name}: {str(e)}")
            return False
    
    def get_configured_providers(self) -> List[str]:
        """Get list of configured providers"""
        configured = []
        for name, provider in self.providers.items():
            if provider.is_configured():
                configured.append(name)
        return configured
    
    async def search_images(
        self,
        query: str,
        provider: Optional[str] = None,
        per_page: int = 10,
        orientation: str = "all",
        category: str = None,
        use_fallback: bool = True
    ) -> Dict[str, Any]:
        """Search for images using specified or default provider"""
        
        # Determine which provider to use
        if provider and provider in self.providers:
            target_provider = provider
        else:
            target_provider = self.default_provider
        
        # Get list of providers to try
        providers_to_try = [target_provider]
        if use_fallback and self.auto_fallback:
            for fallback in self.fallback_providers:
                if fallback != target_provider and fallback not in providers_to_try:
                    providers_to_try.append(fallback)
        
        # Try each provider
        for provider_name in providers_to_try:
            if provider_name not in self.providers:
                continue
            
            provider_instance = self.providers[provider_name]
            if not provider_instance.is_configured():
                logger.warning(f"Provider {provider_name} not configured, skipping")
                continue
            
            try:
                async with provider_instance:
                    result = await provider_instance.search_images(
                        query=query,
                        per_page=per_page,
                        orientation=orientation,
                        category=category
                    )
                
                if result.get("success"):
                    result["provider_used"] = provider_name
                    result["fallback_used"] = provider_name != target_provider
                    return result
                else:
                    logger.warning(f"Provider {provider_name} failed: {result.get('error')}")
                    
            except Exception as e:
                logger.error(f"Error with provider {provider_name}: {str(e)}")
                continue
        
        # All providers failed
        return {
            "success": False,
            "error": "All image providers failed or are not configured",
            "providers_tried": providers_to_try
        }
    
    async def search_multiple_providers(
        self,
        query: str,
        providers: List[str] = None,
        per_page: int = 5
    ) -> Dict[str, Any]:
        """Search multiple providers simultaneously"""
        
        if not providers:
            providers = self.get_configured_providers()
        
        if not providers:
            return {
                "success": False,
                "error": "No configured providers available"
            }
        
        # Create tasks for each provider
        tasks = []
        for provider_name in providers:
            if provider_name in self.providers and self.providers[provider_name].is_configured():
                task = self._search_single_provider(provider_name, query, per_page)
                tasks.append((provider_name, task))
        
        if not tasks:
            return {
                "success": False,
                "error": "No valid providers to search"
            }
        
        # Execute all searches concurrently
        results = {}
        for provider_name, task in tasks:
            try:
                result = await task
                results[provider_name] = result
            except Exception as e:
                logger.error(f"Error searching {provider_name}: {str(e)}")
                results[provider_name] = {
                    "success": False,
                    "error": str(e)
                }
        
        # Combine results
        all_images = []
        successful_providers = []
        
        for provider_name, result in results.items():
            if result.get("success"):
                successful_providers.append(provider_name)
                images = result.get("images", [])
                # Add provider info to each image
                for image in images:
                    image["search_provider"] = provider_name
                all_images.extend(images)
        
        return {
            "success": len(successful_providers) > 0,
            "query": query,
            "providers_searched": list(results.keys()),
            "successful_providers": successful_providers,
            "total_images": len(all_images),
            "images": all_images,
            "provider_results": results
        }
    
    async def _search_single_provider(
        self,
        provider_name: str,
        query: str,
        per_page: int
    ) -> Dict[str, Any]:
        """Search a single provider (helper method)"""
        provider = self.providers[provider_name]
        async with provider:
            return await provider.search_images(query=query, per_page=per_page)
    
    def generate_custom_image(
        self,
        title: str,
        background: Optional[str] = None,
        style: str = "auto",
        size: Optional[tuple] = None
    ) -> Dict[str, Any]:
        """Generate a custom image with title overlay"""
        try:
            return self.custom_generator.generate_image(
                title=title,
                background=background,
                style=style,
                size=size
            )
        except Exception as e:
            logger.error(f"Failed to generate custom image: {str(e)}")
            return {
                "success": False,
                "error": f"Custom image generation failed: {str(e)}"
            }
    
    def add_background(self, image_path: str, name: Optional[str] = None) -> bool:
        """Add a new background for custom image generation"""
        return self.custom_generator.add_background(image_path, name)
    
    def get_available_backgrounds(self) -> List[str]:
        """Get list of available backgrounds"""
        return self.custom_generator.get_available_backgrounds()
    
    def update_text_style(self, language: str, style_updates: Dict[str, Any]) -> bool:
        """Update text style for custom image generation"""
        return self.custom_generator.update_text_style(language, style_updates)
    
    def get_text_styles(self) -> Dict[str, Any]:
        """Get current text styles for custom image generation"""
        return self.custom_generator.get_text_styles()
    
    async def get_image_for_article(
        self,
        title: str,
        category: Optional[str] = None,
        prefer_custom: bool = False,
        keywords: List[str] = None
    ) -> Dict[str, Any]:
        """Get the best image for an article (custom or from providers)"""
        
        if prefer_custom:
            # Try custom generation first
            custom_result = self.generate_custom_image(title)
            if custom_result.get("success"):
                custom_result["image_type"] = "custom_generated"
                return custom_result
        
        # Search for images using keywords or title
        search_query = title
        if keywords:
            # Use keywords for better search results
            search_query = " ".join(keywords[:3])  # Use first 3 keywords
        
        # Try to get images from providers
        search_result = await self.search_images(
            query=search_query,
            category=category,
            per_page=5
        )
        
        if search_result.get("success") and search_result.get("images"):
            # Return the first (best) image
            best_image = search_result["images"][0]
            return {
                "success": True,
                "image_type": "provider_search",
                "provider": search_result.get("provider_used"),
                "image": best_image,
                "search_query": search_query,
                "total_found": len(search_result["images"])
            }
        
        # Fallback to custom generation if search failed
        if not prefer_custom:
            custom_result = self.generate_custom_image(title)
            if custom_result.get("success"):
                custom_result["image_type"] = "custom_fallback"
                return custom_result
        
        return {
            "success": False,
            "error": "Failed to get image from both providers and custom generation"
        }
    
    def get_status(self) -> Dict[str, Any]:
        """Get status of image manager and all providers"""
        provider_status = {}
        for name, provider in self.providers.items():
            provider_status[name] = {
                "configured": provider.is_configured(),
                "name": provider.get_provider_name()
            }
        
        generator_stats = self.custom_generator.get_stats()
        
        return {
            "providers": provider_status,
            "configured_providers": self.get_configured_providers(),
            "default_provider": self.default_provider,
            "custom_generator": {
                "available": True,
                "stats": generator_stats
            },
            "settings": {
                "auto_fallback": self.auto_fallback,
                "fallback_providers": self.fallback_providers
            }
        }
