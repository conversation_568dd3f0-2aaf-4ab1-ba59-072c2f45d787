<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DeepSearch Enhanced News App - تطبيق الأخبار المحسن</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 20px auto;
            max-width: 1400px;
            padding: 30px;
        }
        
        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border-left: 5px solid #667eea;
            transition: transform 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .category-selector {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .category-btn {
            margin: 5px;
            border-radius: 20px;
            padding: 8px 16px;
            border: 2px solid #667eea;
            background: white;
            color: #667eea;
            transition: all 0.3s ease;
        }
        
        .category-btn.active {
            background: #667eea;
            color: white;
        }
        
        .category-btn:hover {
            background: #667eea;
            color: white;
        }
        
        .image-preview {
            max-width: 100%;
            max-height: 300px;
            border-radius: 10px;
            margin: 10px 0;
        }
        
        .article-preview {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .progress-indicator {
            display: none;
            text-align: center;
            padding: 20px;
        }
        
        .step-indicator {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        
        .step {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 10px;
            position: relative;
        }
        
        .step.active {
            background: #667eea;
            color: white;
        }
        
        .step.completed {
            background: #28a745;
            color: white;
        }
        
        .step::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 100%;
            width: 20px;
            height: 2px;
            background: #e9ecef;
            transform: translateY(-50%);
        }
        
        .step:last-child::after {
            display: none;
        }
        
        .config-section {
            background: #fff3cd;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #ffc107;
        }
        
        .result-section {
            background: #d1ecf1;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 4px solid #17a2b8;
        }
        
        .error-section {
            background: #f8d7da;
            border-radius: 10px;
            padding: 15px;
            margin: 15px 0;
            border-left: 4px solid #dc3545;
        }
        
        .tab-content {
            display: none;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .nav-tabs .nav-link {
            border-radius: 10px 10px 0 0;
            margin-right: 5px;
        }
        
        .nav-tabs .nav-link.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Header -->
            <div class="text-center mb-4">
                <h1 class="display-4" style="color: #667eea;">
                    <i class="fas fa-newspaper"></i>
                    DeepSearch Enhanced News App
                </h1>
                <p class="lead text-muted">تطبيق الأخبار المحسن مع إدارة الفئات والصور</p>
            </div>

            <!-- Navigation Tabs -->
            <ul class="nav nav-tabs mb-4" id="mainTabs">
                <li class="nav-item">
                    <a class="nav-link active" href="#article-generator" data-tab="article-generator">
                        <i class="fas fa-edit"></i> إنشاء المقالات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#categories" data-tab="categories">
                        <i class="fas fa-tags"></i> إدارة الفئات
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#images" data-tab="images">
                        <i class="fas fa-images"></i> إدارة الصور
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="#settings" data-tab="settings">
                        <i class="fas fa-cog"></i> الإعدادات
                    </a>
                </li>
            </ul>

            <!-- Article Generator Tab -->
            <div id="article-generator" class="tab-content active">
                <div class="feature-card">
                    <h3><i class="fas fa-edit"></i> إنشاء مقال احترافي</h3>
                    
                    <!-- Category Selection -->
                    <div class="category-selector">
                        <h5>اختر فئة الخبر:</h5>
                        <div id="categoryButtons">
                            <button class="btn category-btn active" data-category="">جميع الفئات</button>
                            <button class="btn category-btn" data-category="gaming">الألعاب</button>
                            <button class="btn category-btn" data-category="cryptocurrency">العملات الرقمية</button>
                            <button class="btn category-btn" data-category="football">كرة القدم</button>
                            <button class="btn category-btn" data-category="technology">التكنولوجيا</button>
                            <button class="btn category-btn" data-category="politics">السياسة</button>
                        </div>
                    </div>
                    
                    <!-- Article Input -->
                    <div class="row">
                        <div class="col-md-8">
                            <label for="articleTopic" class="form-label">موضوع المقال:</label>
                            <input type="text" id="articleTopic" class="form-control" 
                                   placeholder="مثال: آخر أخبار Death Stranding 2">
                        </div>
                        <div class="col-md-4">
                            <label for="articleStyle" class="form-label">نوع المقال:</label>
                            <select id="articleStyle" class="form-select">
                                <option value="professional">احترافي</option>
                                <option value="casual">غير رسمي</option>
                                <option value="news">إخباري</option>
                                <option value="analysis">تحليلي</option>
                            </select>
                        </div>
                    </div>
                    
                    <!-- Image Options -->
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="includeImage" checked>
                                <label class="form-check-label" for="includeImage">
                                    إضافة صورة للمقال
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="preferCustomImage">
                                <label class="form-check-label" for="preferCustomImage">
                                    تفضيل الصور المخصصة
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Generate Button -->
                    <div class="text-center mt-4">
                        <button onclick="generateArticle()" class="btn btn-primary btn-lg">
                            <i class="fas fa-magic"></i> إنشاء المقال
                        </button>
                    </div>
                </div>
                
                <!-- Progress Indicator -->
                <div id="progressIndicator" class="progress-indicator">
                    <div class="step-indicator">
                        <div class="step" id="step1"><i class="fas fa-search"></i></div>
                        <div class="step" id="step2"><i class="fas fa-edit"></i></div>
                        <div class="step" id="step3"><i class="fas fa-image"></i></div>
                        <div class="step" id="step4"><i class="fas fa-check"></i></div>
                    </div>
                    <p id="progressText">جاري البحث عن المعلومات...</p>
                </div>
                
                <!-- Results -->
                <div id="articleResults" style="display: none;">
                    <div class="result-section">
                        <h4>نتيجة إنشاء المقال</h4>
                        <div id="articleContent"></div>
                        <div id="articleImage"></div>
                        <div class="mt-3">
                            <button onclick="publishToBlogger()" class="btn btn-success me-2">
                                <i class="fab fa-blogger"></i> نشر على بلوجر
                            </button>
                            <button onclick="copyHTML()" class="btn btn-info me-2">
                                <i class="fas fa-copy"></i> نسخ HTML
                            </button>
                            <button onclick="downloadArticle()" class="btn btn-secondary">
                                <i class="fas fa-download"></i> تحميل
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Categories Tab -->
            <div id="categories" class="tab-content">
                <div class="feature-card">
                    <h3><i class="fas fa-tags"></i> إدارة فئات الأخبار</h3>
                    
                    <!-- Add New Category -->
                    <div class="config-section">
                        <h5>إضافة فئة جديدة</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <input type="text" id="newCategoryId" class="form-control" placeholder="معرف الفئة">
                            </div>
                            <div class="col-md-3">
                                <input type="text" id="newCategoryNameAr" class="form-control" placeholder="الاسم بالعربية">
                            </div>
                            <div class="col-md-3">
                                <input type="text" id="newCategoryNameEn" class="form-control" placeholder="الاسم بالإنجليزية">
                            </div>
                            <div class="col-md-3">
                                <button onclick="addCategory()" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> إضافة
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Categories List -->
                    <div id="categoriesList">
                        <p>جاري تحميل الفئات...</p>
                    </div>
                </div>
            </div>

            <!-- Images Tab -->
            <div id="images" class="tab-content">
                <div class="feature-card">
                    <h3><i class="fas fa-images"></i> إدارة الصور</h3>
                    
                    <!-- Image Search -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <input type="text" id="imageSearchQuery" class="form-control" 
                                   placeholder="البحث عن صور...">
                        </div>
                        <div class="col-md-3">
                            <select id="imageProvider" class="form-select">
                                <option value="">مزود تلقائي</option>
                                <option value="unsplash">Unsplash</option>
                                <option value="pixabay">Pixabay</option>
                                <option value="pexels">Pexels</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button onclick="searchImages()" class="btn btn-primary">
                                <i class="fas fa-search"></i> بحث
                            </button>
                        </div>
                    </div>
                    
                    <!-- Custom Image Generator -->
                    <div class="config-section">
                        <h5>إنشاء صورة مخصصة</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <input type="text" id="customImageTitle" class="form-control" 
                                       placeholder="عنوان الصورة">
                            </div>
                            <div class="col-md-3">
                                <select id="imageBackground" class="form-select">
                                    <option value="">خلفية عشوائية</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <button onclick="generateCustomImage()" class="btn btn-success">
                                    <i class="fas fa-magic"></i> إنشاء
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Image Results -->
                    <div id="imageResults"></div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div id="settings" class="tab-content">
                <div class="feature-card">
                    <h3><i class="fas fa-cog"></i> إعدادات التطبيق</h3>
                    
                    <!-- API Configuration -->
                    <div class="config-section">
                        <h5>إعدادات APIs الصور</h5>
                        <div class="row mb-2">
                            <div class="col-md-4">
                                <label>Unsplash API Key:</label>
                                <input type="password" id="unsplashApiKey" class="form-control">
                            </div>
                            <div class="col-md-4">
                                <label>Pixabay API Key:</label>
                                <input type="password" id="pixabayApiKey" class="form-control">
                            </div>
                            <div class="col-md-4">
                                <label>Pexels API Key:</label>
                                <input type="password" id="pexelsApiKey" class="form-control">
                            </div>
                        </div>
                        <button onclick="saveImageAPISettings()" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ إعدادات الصور
                        </button>
                    </div>
                    
                    <!-- Blogger Configuration -->
                    <div class="config-section">
                        <h5>إعدادات بلوجر</h5>
                        <div class="row mb-2">
                            <div class="col-md-6">
                                <label>Blog ID:</label>
                                <input type="text" id="blogId" class="form-control">
                            </div>
                            <div class="col-md-6">
                                <label>Service Account JSON:</label>
                                <textarea id="serviceAccountJson" class="form-control" rows="3"></textarea>
                            </div>
                        </div>
                        <button onclick="saveBloggerSettings()" class="btn btn-primary">
                            <i class="fas fa-save"></i> حفظ إعدادات بلوجر
                        </button>
                    </div>
                    
                    <!-- Status -->
                    <div id="systemStatus" class="result-section">
                        <h5>حالة النظام</h5>
                        <p>جاري تحميل حالة النظام...</p>
                    </div>
                </div>
            </div>

            <!-- Error Display -->
            <div id="errorDisplay" class="error-section" style="display: none;">
                <h5><i class="fas fa-exclamation-triangle"></i> خطأ</h5>
                <p id="errorMessage"></p>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="enhanced_news_app.js"></script>
</body>
</html>
